{"uuid": "85e5c1ad-dc8b-465b-98dd-5e9266144174", "historyId": "2ac546550b0f94e3004cfb392a3dd92a", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467578066, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/drop-bonus GET", "stop": 1756467578066}], "attachments": [], "parameters": [], "start": 1756467578016, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 23 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756467578066}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467578075, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756467578075}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467578076, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467578076}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467577968, "name": "GET /utils/drop-bonus -> 417", "fullName": "GET /utils/drop-bonus -> 417", "stop": 1756467578076}