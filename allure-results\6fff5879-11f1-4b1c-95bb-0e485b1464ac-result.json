{"uuid": "6fff5879-11f1-4b1c-95bb-0e485b1464ac", "historyId": "e67bdb718651cb6b435baece0665213f", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467557609, "name": "https://platform-api.dev.estrelabet.bet.br/api/games/get-free-games GET", "stop": 1756467557609}], "attachments": [], "parameters": [], "start": 1756467556683, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 27 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, qs: { gameSymbol: '', language: 'pt-BR', upcomingGames: false }, failOnStatusCode: false }\")", "stop": 1756467557609}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467557624, "name": "Assert skipped Esperado 417. Recebido: 500. Body: {\"error\":\"internal server error\",\"code\":10}: expected 500 to equal 417", "stop": 1756467557624}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 500}, {"name": "expected", "value": 417}], "start": 1756467557624, "name": "assert Esperado 417. Recebido: 500. Body: {\"error\":\"internal server error\",\"code\":10}: expected **500** to equal **417**", "stop": 1756467557624}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467556604, "name": "GET /games/get-free-games -> 417", "fullName": "GET /games/get-free-games -> 417", "stop": 1756467557624}