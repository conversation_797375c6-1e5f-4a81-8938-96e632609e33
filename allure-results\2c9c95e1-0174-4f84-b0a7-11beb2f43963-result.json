{"uuid": "2c9c95e1-0174-4f84-b0a7-11beb2f43963", "historyId": "96fdbac08f11a1e7fd83eb8a9e802033", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468997968, "name": "https://platform-api.dev.estrelabet.bet.br/api/reviews/export GET", "stop": 1756468997968}], "attachments": [], "parameters": [], "start": 1756468997443, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 21 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756468997968}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468997387, "name": "GET /reviews/export -> 417", "fullName": "GET /reviews/export -> 417", "stop": 1756468998015}