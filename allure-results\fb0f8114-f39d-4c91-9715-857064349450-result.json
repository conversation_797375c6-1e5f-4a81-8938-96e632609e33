{"uuid": "fb0f8114-f39d-4c91-9715-************", "historyId": "6a34c958fd2ec827dfdb72c3184e0e4e", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467581664, "name": "https://platform-api.dev.estrelabet.bet.br/api/login/social/integration POST", "stop": 1756467581664}], "attachments": [], "parameters": [], "start": 1756467581607, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 31 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756467581664}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467581671, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"invalid email\",\"code\":11}: expected 400 to equal 417", "stop": 1756467581671}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467581671, "name": "assert Esperado 417. <PERSON><PERSON>bido: 400. Body: {\"error\":\"invalid email\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467581671}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467581545, "name": "POST /login/social/integration -> 417", "fullName": "POST /login/social/integration -> 417", "stop": 1756467581671}