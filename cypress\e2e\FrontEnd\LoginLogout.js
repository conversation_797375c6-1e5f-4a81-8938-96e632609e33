/// <reference types="cypress" />

import loc from '../../support/locators'
import { Utility } from "../../support/utility"
import messages from '../../support/validationMessages'

import '@cypress/grep';
import { blockTrackers } from '../../support/blockTrackers';

const url = new Utility().getBaseUrl();
const urlRequest = new Utility().getApiUrl();

describe('Deve validar o login', () => {
    beforeEach(() => {
        // Bloqueia terceiros e aplica otimizações de performance
        blockTrackers();

        cy.visit(url)
        cy.intercept({
            method: 'POST',
            url: url + '/next/pb/api/login',
        }).as('login')

        //Clicando no botão entrar
        cy.validateText('.nebulosa-header__buttonContainer > button', 'Entrar')
        cy.clickMouse('.nebulosa-header__buttonContainer > button')
    });

    it('Validando elementos da tela', () => {
        //Suporte
        cy.validateText('span.d_block', 'Suporte')
        //Valida logo do cliente
        cy.get(loc.LOGIN.LOGO_CLIENTE)
            .should('be.visible')
            .and('have.attr', 'src')
            .and('include', '/estrelabet/logo');
        //Validação do título e subtítulo
        cy.validateTextContains(messages.VALIDATIONS.LOGIN_PAGE.TITLE)
        //Validando campo email, título e placeholder
        cy.validateText(loc.LOGIN.EMAIL_TITULO, messages.VALIDATIONS.LOGIN_PAGE.EMAIL_TITULO)
        cy.validatePlaceholder(loc.LOGIN.EMAIL_CAMPO, messages.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_EMAIL)
        //Validando campo senha, título e placeholder
        cy.validateText(loc.LOGIN.SENHA_TITULO, messages.VALIDATIONS.LOGIN_PAGE.SENHA_TITULO)
        cy.validatePlaceholder(loc.LOGIN.SENHA_CAMPO, messages.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_SENHA)
        // Digitar senha
        cy.get(loc.LOGIN.SENHA_CAMPO)
            .type('123456').should('have.attr', 'type', 'password'); // Verifica que está oculta
        // Clicar no botão de mostrar senha
        cy.get(loc.LOGIN.ICONE_OLHO_SENHA).should('be.visible').and('exist').click();
        // Verificar que a senha está visível
        cy.get(loc.LOGIN.SENHA_CAMPO).should('have.attr', 'type', 'text');
        // Clicar novamente para ocultar senha
        cy.get(loc.LOGIN.ICONE_OLHO_SENHA).click();
        // Verificar que a senha voltou a ser oculta
        cy.get(loc.LOGIN.SENHA_CAMPO).should('have.attr', 'type', 'password');
        //Botão esqueci a senha
        cy.validateTextContains(messages.VALIDATIONS.RECUPERAR_SENHA.BOTÃO_RECUPERAR_SENHA)
        //Botão entrar desabilitado
        cy.get(loc.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar')
            .and('be.visible')
            .and('be.disabled');
    });

    it('Deve realizar o login com sucesso', () => {
        const username = Cypress.env('user_name')
        const password = Cypress.env('user_password')
        expect(username, 'Nome de usuário').to.be.a('string').and.not.be.empty
        if (typeof password !== 'string' || !password) {
            throw new Error('O valor senha está ausente, inclua a senha usando o cy.env')
        }
        cy.get(loc.LOGIN.EMAIL_CAMPO)
            .type(username)
            .should('have.value', username)
        cy.get(loc.LOGIN.SENHA_CAMPO)
            .type(password, { log: false })
            .should(el$ => {
                if (el$.val() !== password) {
                    throw new Error('Valor diferente da senha digitada')
                }
            })
        cy.clickMouse(loc.LOGIN.BOTAO_ENTRAR)
        cy.wait('@login').its('response.statusCode').should('eq', 200)
        cy.get('@login').then(({ request, response }) => {
            expect(request.method).to.equal('POST')
            cy.isVisible('.nebulosa-avatar__fallback:eq(2)')
        })

    });

    context('Deve validar padrões de email incorreto', () => {

        it('Deve validar login credenciais incorretas', () => {
            cy.typeText(loc.LOGIN.EMAIL_CAMPO, '<EMAIL>')
            cy.typeText(loc.LOGIN.SENHA_CAMPO, '123456Uds@')
            cy.clickMouse(loc.LOGIN.BOTAO_ENTRAR)
            cy.wait('@login').its('response.statusCode').should('eq', 401)
            cy.validateTextContains(messages.FRONT_ERRORS.LOGIN_PAGE.LOGIN_INVALIDO)
        });
        it('Deve validar qtd mínima de caracteres no email', () => {
            cy.typeText(loc.LOGIN.EMAIL_CAMPO, 'qa')
            cy.get(loc.MENSAGENS_ERRO.EMAIL_INVÁLIDO)
                .should('have.text', messages.FRONT_ERRORS.LOGIN_PAGE.EMAIL_DOIS_CARACTERES)

            //Botão entrar desabilitado
            cy.get(loc.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar')
                .and('be.visible')
                .and('be.disabled');
        });

        it('Deve validar qtd mínima de caracteres na senha', () => {
            cy.typeText(loc.LOGIN.SENHA_CAMPO, '1234567')
            cy.get(loc.MENSAGENS_ERRO.COMPLEXIDADE_SENHA_INCORRETA)
                .should('have.text', messages.FRONT_ERRORS.LOGIN_PAGE.SENHA_QTD_INCORRETA)

            //Botão entrar desabilitado
            cy.get(loc.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar')
                .and('be.visible')
                .and('be.disabled');
        });
    });

});
describe('Deve validar o logout', () => {
    beforeEach(() => {
        blockTrackers();
        cy.loginToAuth0(Cypress.env('user_name'), Cypress.env('user_password'))
        cy.intercept({
            method: 'POST',
            url: url + '/logout',
        }).as('logout')

    })
    it('Deve realizar o logout', () => {
        //Validando avatar
        cy.isVisible('nebulosa-avatar__root nebulosa-avatar__root--size_Medium > nebulosa-avatar__root nebulosa-avatar__root--size_Medium')
        //Abrindo opções do usuário
        cy.clickMouse('nebulosa-avatar__root nebulosa-avatar__root--size_Medium > nebulosa-avatar__root nebulosa-avatar__root--size_Medium')
        //Clicando em sair
        cy.contains('section.d_flex > div', 'Sair').should('be.visible').click()
        cy.wait('@logout').its('response.statusCode').should('eq', 200)

    })
});