{"uuid": "65dde750-09fe-48e8-805c-5c9ec13f5373", "historyId": "8eb53d1808ef58088fe3b153f81f08db", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468985039, "name": "https://platform-api.dev.estrelabet.bet.br/api/promotions GET", "stop": 1756468985039}], "attachments": [], "parameters": [], "start": 1756468984320, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 17 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, qs: { languageId: 'pt', hiddenPromotion: false }, failOnStatusCode: false }\")", "stop": 1756468985039}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468985045, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 424. Body: {\"error\":\"third-party failed\",\"code\":58}: expected 424 to equal 417", "stop": 1756468985045}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 424}, {"name": "expected", "value": 417}], "start": 1756468985046, "name": "assert Esperado 417. <PERSON><PERSON>bido: 424. Body: {\"error\":\"third-party failed\",\"code\":58}: expected **424** to equal **417**", "stop": 1756468985046}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468984237, "name": "GET /promotions -> 417", "fullName": "GET /promotions -> 417", "stop": 1756468985046}