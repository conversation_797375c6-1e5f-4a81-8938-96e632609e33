{"uuid": "f0d26608-162c-4bf1-a5d0-926e46d85ae4", "historyId": "7f9cb97afa39e6fcebf2059d8f426d79", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468981832, "name": "https://platform-api.dev.estrelabet.bet.br/api/login POST", "stop": 1756468981832}], "attachments": [], "parameters": [], "start": 1756468981592, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 12 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: { email: '<EMAIL>', password: 'Password123' }, failOnStatusCode: false }\")", "stop": 1756468981832}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468981839, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Re<PERSON>bido: 500. Body: undefined: expected 500 to equal 417", "stop": 1756468981839}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 500}, {"name": "expected", "value": 417}], "start": 1756468981840, "name": "assert Esperado 417. <PERSON><PERSON><PERSON><PERSON>: 500. Body: undefined: expected **500** to equal **417**", "stop": 1756468981840}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468981526, "name": "POST /login -> 417", "fullName": "POST /login -> 417", "stop": 1756468981840}