{"uuid": "a51d50aa-35f7-4b4c-ace1-6f44f51e03aa", "historyId": "09398ffd0d5de28a13c04ab5de21e14a", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468996094, "name": "https://platform-api.dev.estrelabet.bet.br/api/profile/update-profile POST", "stop": 1756468996094}], "attachments": [], "parameters": [], "start": 1756468995996, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 29 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756468996094}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468996111, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756468996111}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468996111, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468996111}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468995907, "name": "POST /profile/update-profile -> 417", "fullName": "POST /profile/update-profile -> 417", "stop": 1756468996111}