{"uuid": "4858a943-bbff-4d97-9542-5f1c915eda42", "historyId": "2ac546550b0f94e3004cfb392a3dd92a", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469001782, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/drop-bonus GET", "stop": 1756469001782}], "attachments": [], "parameters": [], "start": 1756469001677, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 23 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756469001782}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469001793, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756469001793}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756469001794, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756469001794}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756469001610, "name": "GET /utils/drop-bonus -> 417", "fullName": "GET /utils/drop-bonus -> 417", "stop": 1756469001794}