const fs = require('fs');
const path = 'cypress/support/postman.json';
const backup = 'cypress/support/postman.backup.json';

const endpointsToRemove = [
  '/api/v3/cashier/deposit/account',
  '/api/v3/cashier/deposit/queued-notifications',
  '/api/v3/cashier/deposit/screen-info',
  '/api/v3/cashier/deposit/wallet-payment',
  '/api/v3/cashier/deposit/queue-notification',
  '/api/v3/cashier/deposit/wallet-withdrawal',
  '/api/v3/games/cache/all',
  '/api/v3/games/get-free-games',
  '/api/v3/games/get-group-games',
  '/api/v3/games/get-list-group',
  '/api/v3/games/get-real-games',
  '/api/v3/login',
  '/api/v3/login/logout',
  '/api/v3/login/social',
  '/api/v3/profile',
  '/api/v3/profile/balance',
  '/api/v3/profile/kyc-url',
  '/api/v3/profile/last-played-games',
  '/api/v3/profile/send-auth-code',
  '/api/v3/profile/verify-auth-code',
  '/api/v3/promotions',
  '/api/v3/promotions/{promotionId}',
  '/api/v3/utils/address',
];

function removeEndpointBlock(content, endpoint) {
  // Find the key pattern with optional whitespace: "<endpoint>"\s*:
  const esc = endpoint.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  const re = new RegExp('"' + esc + '"\\s*:', 'm');
  let m = re.exec(content);
  if (!m) {
    // try with escaped slashes
    const epEsc = endpoint.replaceAll('/', '\\/');
    const esc2 = epEsc.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const re2 = new RegExp('"' + esc2 + '"\\s*:', 'm');
    m = re2.exec(content);
    if (!m) return { content, removed: false };
  }
  const idx = m.index;

  // Move to the first '{' after the colon
  let braceStart = content.indexOf('{', idx);
  if (braceStart === -1) return { content, removed: false };

  // Count braces to find the matching closing brace for this object
  let i = braceStart;
  let depth = 0;
  let inString = false;
  let escaped = false;
  for (; i < content.length; i++) {
    const ch = content[i];
    if (inString) {
      if (escaped) {
        escaped = false;
      } else if (ch === '\\') {
        escaped = true;
      } else if (ch === '"') {
        inString = false;
      }
      continue;
    }
    if (ch === '"') {
      inString = true;
      continue;
    }
    if (ch === '{') depth++;
    else if (ch === '}') {
      depth--;
      if (depth === 0) { i++; break; }
    }
  }
  if (depth !== 0) return { content, removed: false };

  // Include optional trailing comma and whitespace/newlines after the object
  let end = i;
  while (end < content.length && /[\s\n\r\t,]/.test(content[end])) {
    // Stop at the next quote starting a new key at the same indentation level
    if (content[end] === '"') break;
    end++;
  }

  // Also try to remove a preceding comma if there is one and we are not at the beginning of the paths object
  let start = idx;
  // Move start to beginning of the line for cleaner removal
  while (start > 0 && content[start - 1] !== '\n') start--;

  // If the character before start (skipping whitespace) is a comma, remove it instead of the comma after
  let prev = start - 1;
  while (prev >= 0 && /[\s\n\r\t]/.test(content[prev])) prev--;
  if (prev >= 0 && content[prev] === ',') {
    // Remove the preceding comma and any whitespace before start
    start = prev;
    while (start > 0 && /[\s\n\r\t]/.test(content[start - 1])) start--;
  }

  const newContent = content.slice(0, start) + content.slice(end);
  return { content: newContent, removed: true };
}

(function main() {
  const original = fs.readFileSync(path, 'utf8');
  fs.writeFileSync(backup, original);
  let content = original;
  let removedAny = false;
  for (const ep of endpointsToRemove) {
    let result = removeEndpointBlock(content, ep);
    if (!result.removed) {
      // Try again with escaped slash variant (some generators escape slashes)
      const epEsc = ep.replaceAll('/', '\\/');
      result = removeEndpointBlock(content, epEsc);
    }
    content = result.content;
    removedAny ||= result.removed;
    console.log(`${ep}: ${result.removed ? 'REMOVIDO' : 'NAO_ENCONTRADO'}`);
  }
  fs.writeFileSync(path, content);
  console.log('Concluido. Backup em', backup, '| Removidos:', removedAny);
})();

