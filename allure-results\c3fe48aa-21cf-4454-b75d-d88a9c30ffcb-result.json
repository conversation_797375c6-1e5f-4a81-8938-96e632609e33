{"uuid": "c3fe48aa-21cf-4454-b75d-d88a9c30ffcb", "historyId": "d098378bba43067fa054beef0f69d0a9", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467575712, "name": "https://platform-api.dev.estrelabet.bet.br/api/stories GET", "stop": 1756467575712}], "attachments": [], "parameters": [], "start": 1756467575648, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 14 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756467575712}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467575722, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"Invalid story type. Must be 'casino' or 'sportsbook'.\",\"code\":11}: expected 400 to equal 417", "stop": 1756467575722}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467575722, "name": "assert Esperado 417. <PERSON><PERSON>bido: 400. Body: {\"error\":\"Invalid story type. Must be 'casino' or 'sportsbook'.\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467575722}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467575586, "name": "GET /stories -> 417", "fullName": "GET /stories -> 417", "stop": 1756467575722}