{"uuid": "3255dd04-35d0-4f17-92f7-dc52493ae522", "historyId": "f8223caf82f3c29cdc5a26cb5e485da2", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468977227, "name": "https://platform-api.dev.estrelabet.bet.br/api/cashier/deposit/queued-notifications GET", "stop": 1756468977227}], "attachments": [], "parameters": [], "start": 1756468977167, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 43 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756468977227}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468977088, "name": "GET /cashier/deposit/queued-notifications -> 417", "fullName": "GET /cashier/deposit/queued-notifications -> 417", "stop": 1756468977236}