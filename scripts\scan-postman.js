const fs=require('fs');
const s=fs.readFileSync('cypress/support/postman.json','utf8');
const endpoints=[
'/api/v3/cashier/deposit/account',
'/api/v3/cashier/deposit/queued-notifications',
'/api/v3/cashier/deposit/screen-info',
'/api/v3/cashier/deposit/wallet-payment',
'/api/v3/cashier/deposit/queue-notification',
'/api/v3/cashier/deposit/wallet-withdrawal',
'/api/v3/games/cache/all',
'/api/v3/games/get-free-games',
'/api/v3/games/get-group-games',
'/api/v3/games/get-list-group',
'/api/v3/games/get-real-games',
'/api/v3/login',
'/api/v3/login/logout',
'/api/v3/login/social',
'/api/v3/profile',
'/api/v3/profile/balance',
'/api/v3/profile/kyc-url',
'/api/v3/profile/last-played-games',
'/api/v3/profile/send-auth-code',
'/api/v3/profile/verify-auth-code',
'/api/v3/promotions',
'/api/v3/promotions/{promotionId}',
'/api/v3/utils/address',
];
for(const ep of endpoints){
  const key1='"'+ep+'":';
  const key2='\"'+ep.replaceAll('/','\\/')+'\"\s*:';
  const i=s.indexOf(key1);
  console.log(ep, 'idx1', i);
}

