{"uuid": "c0cde141-5d5e-4a9d-aba3-2841f5c557b3", "historyId": "129c68fb255491285b9a6c79f0de1f81", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468981167, "name": "https://platform-api.dev.estrelabet.bet.br/api/cashier/deposit/wallet-withdrawal POST", "stop": 1756468981167}], "attachments": [], "parameters": [], "start": 1756468981107, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 40 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: { amount: 1, method: 'pix' }, failOnStatusCode: false }\")", "stop": 1756468981167}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468981176, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"invalid session\",\"code\":11}: expected 400 to equal 417", "stop": 1756468981176}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468981176, "name": "assert Esperado 417. <PERSON><PERSON>bido: 400. Body: {\"error\":\"invalid session\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468981176}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468981045, "name": "POST /cashier/deposit/wallet-withdrawal -> 417", "fullName": "POST /cashier/deposit/wallet-withdrawal -> 417", "stop": 1756468981177}