{"uuid": "63c6b9bc-390f-4c5d-aa3e-20cea7b14d65", "historyId": "91f2bd36752e53b835c0d294ec80e34e", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469002846, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/referral-eligible GET", "stop": 1756469002846}], "attachments": [], "parameters": [], "start": 1756469002742, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 30 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, failOnStatusCode: false }\")", "stop": 1756469002846}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469002858, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756469002858}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756469002858, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756469002858}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756469002678, "name": "GET /utils/referral-eligible -> 417", "fullName": "GET /utils/referral-eligible -> 417", "stop": 1756469002858}