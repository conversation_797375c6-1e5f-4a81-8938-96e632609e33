{"uuid": "df36f265-8774-4cee-957a-4013e2cf374b", "historyId": "51408e040d0312578cfb4fc6ffa84bb3", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467571559, "name": "https://platform-api.dev.estrelabet.bet.br/api/management/sportsBetting POST", "stop": 1756467571559}], "attachments": [], "parameters": [], "start": 1756467571496, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 31 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756467571559}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467571567, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"Invalid sports betting data\",\"code\":11}: expected 400 to equal 417", "stop": 1756467571567}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467571568, "name": "assert Esperado 417. <PERSON><PERSON>bid<PERSON>: 400. Body: {\"error\":\"Invalid sports betting data\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467571568}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467571448, "name": "POST /management/sportsBetting -> 417", "fullName": "POST /management/sportsBetting -> 417", "stop": 1756467571568}