{"uuid": "5f124ec6-a04b-4f9d-88c8-8e4e38e1b368", "historyId": "9eacb9cd9db5542ec27f0657176cd215", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467579248, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/social POST", "stop": 1756467579248}], "attachments": [], "parameters": [], "start": 1756467579041, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 19 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756467579248}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467579260, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"invalid body data\",\"code\":11}: expected 400 to equal 417", "stop": 1756467579260}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467579261, "name": "assert Esperado 417. <PERSON><PERSON>bido: 400. Body: {\"error\":\"invalid body data\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467579261}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467578983, "name": "POST /utils/social -> 417", "fullName": "POST /utils/social -> 417", "stop": 1756467579261}