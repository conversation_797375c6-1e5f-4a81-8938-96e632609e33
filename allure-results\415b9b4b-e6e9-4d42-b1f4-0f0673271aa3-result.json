{"uuid": "415b9b4b-e6e9-4d42-b1f4-0f0673271aa3", "historyId": "8d7dda76f23e919435de1c4dce53c73f", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468989671, "name": "https://platform-api.dev.estrelabet.bet.br/api/home/<USER>", "stop": 1756468989671}], "attachments": [], "parameters": [], "start": 1756468989597, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 20 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756468989671}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468989679, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"platform not recognized\",\"code\":11}: expected 400 to equal 417", "stop": 1756468989679}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468989680, "name": "assert Esperado 417. Re<PERSON>bido: 400. Body: {\"error\":\"platform not recognized\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468989680}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468989522, "name": "GET /home/<USER>", "fullName": "GET /home/<USER>", "stop": 1756468989680}