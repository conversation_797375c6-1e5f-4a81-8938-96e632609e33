{"uuid": "5536c747-629b-48e3-acb3-0afe19ab5b96", "historyId": "9470c7be44fbeb6e36da7d0986418924", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": *************, "name": "https://platform-api.dev.estrelabet.bet.br/api/cashier/deposit/update-bank-account POST", "stop": *************}], "attachments": [], "parameters": [], "start": *************, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 42 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: {}, failOnStatusCode: false }\")", "stop": *************}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": *************, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": *************}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": *************, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": *************}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": *************, "name": "POST /cashier/deposit/update-bank-account -> 417", "fullName": "POST /cashier/deposit/update-bank-account -> 417", "stop": *************}