// Plugin personalizado para adicionar informações de API ao relatório Mochawesome
// Este arquivo estende o comportamento padrão do Mochawesome para incluir dados de resposta da API

const originalAddTest = require('mochawesome/lib/addTest');

// Hook para interceptar e modificar os dados do teste antes de serem adicionados ao relatório
function addApiDataToTest(test, stats) {
  // Verifica se o teste tem dados de API anexados
  if (test.ctx && test.ctx.apiResponse) {
    const apiData = test.ctx.apiResponse;
    
    // Adiciona as informações da API ao contexto do teste
    if (!test.context) {
      test.context = '';
    }
    
    // Formata as informações da API para exibição no relatório
    const apiInfo = `
      <div class="api-response-info" style="margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-left: 4px solid #28a745; border-radius: 4px;">
        <h4 style="color: #28a745; margin: 0 0 8px 0;">✅ API Response Details</h4>
        <div style="font-family: monospace; font-size: 12px;">
          <div><strong>Status Code:</strong> <span style="color: #28a745; font-weight: bold;">${apiData.statusCode}</span></div>
          <div><strong>Method:</strong> ${apiData.method}</div>
          <div><strong>URL:</strong> ${apiData.url}</div>
          <div><strong>Timestamp:</strong> ${apiData.timestamp}</div>
          <div style="margin-top: 8px;"><strong>Response Body:</strong></div>
          <pre style="background-color: #f1f3f4; padding: 8px; border-radius: 4px; margin: 4px 0; max-height: 200px; overflow-y: auto; white-space: pre-wrap; word-wrap: break-word;">${JSON.stringify(apiData.responseBody, null, 2)}</pre>
        </div>
      </div>
    `;
    
    test.context += apiInfo;
  }
  
  return test;
}

// Exporta a função para ser usada pelo Cypress
module.exports = {
  addApiDataToTest
};
