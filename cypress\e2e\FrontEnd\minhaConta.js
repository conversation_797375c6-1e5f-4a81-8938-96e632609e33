/// <reference types="cypress" />

import loc from '../../support/locators'
import { Utility } from "../../support/utility"
import '@cypress/grep'
import { blockTrackers } from '../../support/blockTrackers'
import '@shelex/cypress-allure-plugin'

const url = new Utility().getBaseUrl();
const urlRequest = new Utility().getApiUrl();
const urlBff = new Utility().getApiBffUrl();

describe('Deve validar opções da conta', () => {
  beforeEach(() => {
     blockTrackers();
    cy.loginToAuth0(Cypress.env('user_name'), Cypress.env('user_password'))
  });

  it('Validando pagina da minha conta', () => {
    //Validando avatar
    cy.isVisible('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback')
    //Abrindo opções do usuário
    cy.clickMouse('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback')

    //Validando opção Minha conta e acessando
    cy.validateText(':nth-child(4) > :nth-child(2) > .fw_regular', 'Minha conta')
    cy.clickMouse(':nth-child(4) > :nth-child(2) > .fw_regular')

    //validando opções de acesso do usuário
    cy.get('[role="menuitem"] > label:eq(0)').should('have.text', 'Conta').and('be.visible')
    cy.get('[role="menuitem"] > label:eq(1)').should('have.text', 'Carteira').and('be.visible')
    cy.get('[role="menuitem"] > label:eq(2)').should('have.text', 'Bônus').and('be.visible')
    cy.get('[role="menuitem"] > label:eq(3)').should('have.text', 'Apostas').and('be.visible')
    cy.get('[role="menuitem"] > label:eq(4)').should('have.text', 'Limites').and('be.visible')
    cy.get('[role="menuitem"] > label:eq(5)').should('have.text', 'Segurança').and('be.visible')

    cy.log('Validando modal Minha conta')
    //Titulo e subtítulo da área de dados pessoais
    cy.validateText('.fs_lg', 'Minha conta')
    cy.validateText('.mb_md > .fs_md', 'Meu perfil')

    //Validando área de dados pessoais
    cy.contains('Dados Pessoais').should('be.visible')
    cy.validateTextContains('Ver dados pessoais')
    cy.validateText('button > :nth-child(3):eq(0)', 'Visualizar')
    cy.validateText('.nebulosa-badge__root--type_success > label:eq(0)', 'Completo')

    //Validando área email
    cy.contains('E-mail').should('be.visible')
    cy.isVisible('[data-webview-id="HidenElement"] > .d_flex > div > .lh_lg:eq(1)')
    cy.validateText('button > :nth-child(3):eq(1)', 'Editar')
    cy.validateText('.nebulosa-badge__root--type_success > label:eq(1)', 'Completo')
    //Validando área telefone
    cy.contains('Telefone').should('be.visible')
    cy.isVisible('[data-webview-id="HidenElement"] > .d_flex > div > .lh_lg:eq(2)')
    cy.validateText('button > :nth-child(3):eq(2)', 'Editar')
    cy.validateText('.nebulosa-badge__root--type_success > label:eq(2)', 'Completo')
    //Validando área endereço
    cy.contains('Endereço').should('be.visible')
    cy.isVisible('[data-webview-id="HidenElement"] > .d_flex > div > .lh_lg:eq(3)')
    cy.validateText('button > :nth-child(3):eq(3)', 'Editar')
    cy.validateText('.nebulosa-badge__root--type_success > label:eq(3)', 'Completo')

    cy.log('Validando modal alterar senha')
    cy.validateText('.mb_sm > h2:eq(0)', 'Alterar senha')
    cy.validateText('.mb_lg > div > div > label:eq(0)', 'Senha atual')
    cy.validateText('.mb_lg > div > div > label:eq(1)', 'Criar nova senha')
    cy.validateText('.mb_lg > div > div > label:eq(2)', 'Confirme a nova senha')
    cy.get('[type="submit"]:eq(0)').should('have.text', 'Salvar').and('be.disabled')

    cy.log('Validando modal preferencia de comunicação')
    cy.get('[type="submit"]:eq(1)').should('have.text', 'Salvar').and('be.disabled')
    cy.validateText('.mb_sm > h2:eq(1)', 'Preferências de comunicação')
    cy.validateText('.my_md', 'Quero receber notificações relacionadas às minhas solicitações de depósito/saque, apostas grátis, promoções e comunicações de marketing personalizadas da EstrelaBet por:')
    //Opção checkbox
    cy.validateText('[for="emailSubscribed"]', 'E-mail')
    cy.validateText('[for="mobileSubscribed"]', 'SMS')
    //email
    cy.get('[role="checkbox"]:eq(0)').should('have.attr', 'aria-checked', 'true')
    cy.clickMouse('[role="checkbox"]:eq(0)')
    cy.get('[role="checkbox"]:eq(0)').should('have.attr', 'aria-checked', 'false')
    cy.clickMouse('[role="checkbox"]:eq(0)')
    cy.get('[role="checkbox"]:eq(0)').should('have.attr', 'aria-checked', 'true')

    //sms
    cy.get('[role="checkbox"]:eq(1)').should('have.attr', 'aria-checked', 'true')
    cy.clickMouse('[role="checkbox"]:eq(1)')
    cy.get('[role="checkbox"]:eq(1)').should('have.attr', 'aria-checked', 'false')
    cy.clickMouse('[role="checkbox"]:eq(1)')
    cy.get('[role="checkbox"]:eq(1)').should('have.attr', 'aria-checked', 'true')

  });
});

describe('Deve validar carteira', () => {
  beforeEach(() => {
    blockTrackers();
    cy.loginToAuth0(Cypress.env('user_name'), Cypress.env('user_password'))

    cy.clickMouse(loc.ACESSANDO_OPÇÕES_USUÁRIO.ACESSO_MENU)
    //Validando opção Minha conta e acessando
    cy.get(loc.ACESSANDO_OPÇÕES_USUÁRIO.ACESSO_CARTEIRA, { timeout: 10000 })
      .should('have.text', 'Carteira').and('be.visible')
    cy.clickMouse(loc.ACESSANDO_OPÇÕES_USUÁRIO.ACESSO_CARTEIRA)
  });

  it('Deve validar pagina minha carteira', () => {
    cy.log('Validando área de saldo')
    //Título
    cy.validateText('h1', 'Carteira')
    //Validando textos e componentes da area de saldo
    cy.validateText(loc.CARTEIRA.SALDO_TOTAL, 'Saldo total')
    cy.get('p.fs_lg').should('contain.text', 'R$');
    //Saldo livre para saque
    cy.validateText(loc.CARTEIRA.SALDO_LIVRE_PARA_SAQUE, 'Saldo livre para saque')
    cy.get('.d_flex > p.fs_sm:eq(2)').should('contain.text', 'R$');
    //Bônus esportes
    cy.validateText(loc.CARTEIRA.BONUS_ESPORTES, 'Bônus esportes')
    cy.get('div.flex-d_row > .flex-d_column > p.fs_xs:eq(1)').should('contain.text', 'R$');
    //Bonus cassino
    cy.validateText(loc.CARTEIRA.BONUS_CASSINO, 'Bônus cassino')
    cy.get('div.flex-d_row > .flex-d_column > p.fs_xs:eq(2)').should('contain.text', 'R$');
    //Saldo restrito
    cy.validateText(loc.CARTEIRA.SALDO_RESTRITO, 'Saldo restrito')
    cy.get('div.flex-d_row > .flex-d_column > p.fs_xs:eq(3)').should('contain.text', 'R$');
    //botões de ações
    cy.validateText(loc.CARTEIRA.BOTAO_DEPOSITAR, 'Depositar')
    cy.validateText(loc.CARTEIRA.BOTAO_SACAR, 'Sacar')

    cy.log('Validando área das minhas transações')
    //Titulo
    cy.validateText('h2:eq(1)', 'Minhas transações')
    cy.contains('button > span', 'Ver minhas transações').should('be.visible').click()
    //Validando título do periodo de e até
    cy.validateText('.d_flex > label:eq(1)', 'Período de')
    cy.validateText('.d_flex > label:eq(2)', 'Período até')

    //Tipo de transação
    cy.validateText('.nebulosa-select__Wrapper > label', 'Tipo de transação')
    cy.clickMouse('[role="combobox"]:eq(0)')
    const tipoTransacao = [
      'Depósito',
      'Saque cancelado',
      'Saque',
      'Ajuste de saldo positivo',
      'Ajuste de saldo negativo',
      'Todos/Todas',

    ]
    cy.get('[role="option"] > span')
      .should('have.length', 6)
      .then($items => {
        const firstSix = $items.slice(0, 11);
        firstSix.each((index, item) => {
          expect(Cypress.$(item).text()).to.contain(tipoTransacao[index]);
        });
      });
    cy.get('body').type('{esc}') //Fechando o select
    //Botão filtrar
    cy.validateText('[type="submit"]', 'Filtrar')

    //Validando título das colunas da tabela
    cy.validateText('th:eq(0)', 'Status')
    cy.validateText('th:eq(1)', 'Data e hora')
    cy.validateText('th:eq(2)', 'Detalhe da transação')
    cy.validateText('th:eq(3)', 'Transação')
    cy.validateText('th:eq(4)', 'Saldo anterior')
    cy.validateText('th:eq(5)', 'Valor da transação')
    cy.validateText('th:eq(6)', 'Saldo final')

    cy.validateText('td > p', 'Nenhuma transação disponível')
    //Botão solicitar histórico
    cy.validateText('[lefticon="utility-square-list-solid"]', 'Solicitar histórico de 36 meses')

    cy.log('Validando modal dos dados bancários')
    //Título
    cy.validateText('section > h3', 'Dados bancários')
    cy.validateText('section > p', 'Certifique-se de que as chaves Pix e conta bancária esteja vinculada ao seu CPF.')

    //Informações da conta
    cy.validateText('article > .fw_bold', 'Chave(s) Pix')
    cy.isVisible('li > span:eq(0)')
    cy.isVisible('li > span:eq(1)')
    cy.isVisible('li > span:eq(2)')
    cy.validateText('.gap_xs > .gap_md > .nebulosa-button__root > .nebulosa-button__buttonLabel', 'Alterar dados')

    cy.log('Validando área de informe de rendimento')

    cy.validateText('.gap_xxxs > h3:eq(1)', 'Informe de rendimento')
    cy.validateText('h4', 'Solicite para o seu imposto de renda')
    cy.contains('div.d_flex > .fs_xs', 'Ano fiscal').should('be.visible')
    //Componente de ano fiscal 
    cy.isVisible(':nth-child(1) > .nebulosa-select__Wrapper')
    cy.get('.d_flex > div > .flex-d_column > button:eq(1)').should('have.text', 'Solicitar informe').and('be.disabled')
    cy.contains('.fs_xxs', 'Em breve disponível. Fique ligado!').should('be.visible')
  });
})