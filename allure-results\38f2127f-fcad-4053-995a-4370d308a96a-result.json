{"uuid": "38f2127f-fcad-4053-995a-4370d308a96a", "historyId": "dba2a27830cdec3ee3cc921aa44cf1f0", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469004164, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/validatecpf POST", "stop": 1756469004164}], "attachments": [], "parameters": [], "start": 1756469004058, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 24 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: { cpf: '00000000000' }, failOnStatusCode: false }\")", "stop": 1756469004164}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469004174, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Re<PERSON>bido: 500. Body: undefined: expected 500 to equal 417", "stop": 1756469004174}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 500}, {"name": "expected", "value": 417}], "start": 1756469004175, "name": "assert Esperado 417. <PERSON><PERSON><PERSON><PERSON>: 500. Body: undefined: expected **500** to equal **417**", "stop": 1756469004175}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756469004013, "name": "POST /utils/validatecpf -> 417", "fullName": "POST /utils/validatecpf -> 417", "stop": 1756469004175}