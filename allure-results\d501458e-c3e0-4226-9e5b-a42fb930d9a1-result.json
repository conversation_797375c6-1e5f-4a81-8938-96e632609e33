{"uuid": "d501458e-c3e0-4226-9e5b-a42fb930d9a1", "historyId": "a60e7aed8bc244e61222b3f70771ec84", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467563553, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/address GET", "stop": 1756467563553}], "attachments": [], "parameters": [], "start": 1756467563116, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 20 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', cep: '01001000' }, failOnStatusCode: false }\")", "stop": 1756467563553}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467563054, "name": "GET /utils/address -> 417", "fullName": "GET /utils/address -> 417", "stop": 1756467563567}