{"uuid": "fc845269-1872-4a04-ae73-6ad276dc2f2d", "historyId": "d1b1b3720077f6940fa0a9d19350ab61", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467566348, "name": "https://platform-api.dev.estrelabet.bet.br/api/home GET", "stop": 1756467566348}], "attachments": [], "parameters": [], "start": 1756467566099, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 11 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, qs: { platform: 'web' }, failOnStatusCode: false }\")", "stop": 1756467566348}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467566031, "name": "GET /home -> 417", "fullName": "GET /home -> 417", "stop": 1756467566362}