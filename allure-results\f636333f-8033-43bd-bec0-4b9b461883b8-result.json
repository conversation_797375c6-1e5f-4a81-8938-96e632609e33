{"uuid": "f636333f-8033-43bd-bec0-4b9b461883b8", "historyId": "7e408546112c2e86bb84d8034371cc60", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469001189, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/claim-bonus GET", "stop": 1756469001189}], "attachments": [], "parameters": [], "start": 1756469001103, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 24 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, qs: { accept: true }, failOnStatusCode: false }\")", "stop": 1756469001189}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469001198, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756469001198}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756469001198, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756469001198}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756469001055, "name": "GET /utils/claim-bonus -> 417", "fullName": "GET /utils/claim-bonus -> 417", "stop": 1756469001199}