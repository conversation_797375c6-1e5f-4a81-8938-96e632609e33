{"uuid": "516a47b7-83d7-4014-8f45-b89c00de111f", "historyId": "81fee50842131735e0df87c469443abd", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467561707, "name": "https://platform-api.dev.estrelabet.bet.br/api/profile/verify-auth-code POST", "stop": 1756467561707}], "attachments": [], "parameters": [], "start": 1756467561653, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 31 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: { code: '123456' }, failOnStatusCode: false }\")", "stop": 1756467561707}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467561715, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756467561715}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467561715, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467561715}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467561596, "name": "POST /profile/verify-auth-code -> 417", "fullName": "POST /profile/verify-auth-code -> 417", "stop": 1756467561716}