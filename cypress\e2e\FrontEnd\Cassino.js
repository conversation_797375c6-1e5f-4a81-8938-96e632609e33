/// <reference types="cypress" />

import loc from '../../support/locators'
import { Utility } from "../../support/utility"
import '@cypress/grep'
import { blockTrackers } from '../../support/blockTrackers'
import '@shelex/cypress-allure-plugin'

const url = new Utility().getBaseUrl();

describe('Deve abrir a página de cassino', () => {
    beforeEach(() => {
        // Bloqueia terceiros e aplica otimizações de performance
        blockTrackers();

        cy.loginToAuth0(Cypress.env('user_name'), Cypress.env('user_password'))

        cy.intercept({
            method: 'GET',
            url: url + '/cassino-online',
        }).as('getCassino')

        //cy.get('.nebulosa-avatar__fallback:eq(2)', { timeout: 18000 })
        //  .should('have.text', 'AC')
    });

    it('Acessa pagina de Cassino e valida elementos', () => {
        cy.contains('a', 'Cassino')
        cy.contains('a', 'Cassino').click();

        // Valida o botão de busca (ícone de lupa)
        cy.get('a[href="/jogos/busca"]')
            .should('be.visible');

        // Lista de labels esperadas
        const botoes = [
            'Inicial',
            'Torneios',
            'Slots',
            'Crash Games',
            'Comprar Bônus',
            'Novos Jogos',
            'Jackpots',
            'Mais'
        ];

        // Valida cada botão
        botoes.forEach((label) => {
            cy.contains('.nebulosa-button__buttonLabel', label)
                .should('be.visible');
        });

        cy.clickByContains('.nebulosa-button__buttonLabel', 'Mais')

        // Lista de labels esperadas > Tem que pensar em uma logica trazendo o fluxo do back na BFF
        const categorias = [
            'Populares',
            'Jogos de Fortuna',
            'Games Global',
            'Estrela Indica',
            'Novos Jogos',
            'Pragmatic Play',
            'Missão Lucky Rush',
            'Crash Games',
            //'Plinko', removido 26/08 
            'Comprar Bônus',
            'Slots',
            'Jogos Ao Vivo',
            'Jackpots',
            'Em breve',
            'Provedores'
        ];

        // Valida cada label visível
        categorias.forEach((label) => {
            cy.contains('li', label).should('be.visible');
        });

        // Fecha o modal (botão X no header)
        cy.get('.nebulosa-modal__HeaderRight .nebulosa-modal__IconAction')
            .click();

        // Valida que o modal fechou
        cy.get('[role="dialog"]').should('not.exist');

        cy.clickByContains('div.d_flex > div > div > div > h2', 'Populares');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Jogos de Fortuna');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Estrela Indica');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Novos Jogos');
        cy.clickByContains(':nth-child(5) > .ov-y_hidden > .jc_space-between > .desktop\\:ai_center > .jc_start', 'Mais premiados');
        cy.clickByContains(':nth-child(6) > .ov-y_hidden > .jc_space-between > .desktop\\:ai_center > .jc_start', 'Menos premiados');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Pragmatic Play');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Crash Games');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Comprar Bônus');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Slots');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Jogos Ao Vivo');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Jackpots');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Em breve');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Provedores de jogos');

    });

    it('Acessa pagina de Cassino e acessar um jogo', () => {
        cy.contains('a', 'Cassino')
        cy.contains('a', 'Cassino').click();

        // Acessar jogos
        cy.get('img[alt="Aviator-game-img"]').click()

        //Caso o usuario nao tenha verificado a identidade, clica no botão "Não quero verificar agora"
        cy.get('body').then(($body) => {
            // Verifica se o botão existe
            if ($body.find('button:contains("Não quero verificar agora")').length) {
                cy.contains('button', 'Não quero verificar agora').click();
            } else {
                cy.log('Modal não apareceu, continuando o teste...');
            }
        });
    });  
});
