{"uuid": "146cfc0a-c095-4300-bd4a-b307b6df9486", "historyId": "bb6e8a4a73a67ffcfecd65c2afe99dfe", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467556007, "name": "https://platform-api.dev.estrelabet.bet.br/api/cashier/deposit/screen-info GET", "stop": 1756467556007}], "attachments": [], "parameters": [], "start": 1756467555950, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 34 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, qs: { platform: 'web' }, failOnStatusCode: false }\")", "stop": 1756467556007}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467556019, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756467556019}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467556020, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467556020}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467555902, "name": "GET /cashier/deposit/screen-info -> 417", "fullName": "GET /cashier/deposit/screen-info -> 417", "stop": 1756467556020}