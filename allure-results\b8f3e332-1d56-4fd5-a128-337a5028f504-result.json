{"uuid": "b8f3e332-1d56-4fd5-a128-337a5028f504", "historyId": "bb6e8a4a73a67ffcfecd65c2afe99dfe", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468977606, "name": "https://platform-api.dev.estrelabet.bet.br/api/cashier/deposit/screen-info GET", "stop": 1756468977606}], "attachments": [], "parameters": [], "start": 1756468977545, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 34 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, qs: { platform: 'web' }, failOnStatusCode: false }\")", "stop": 1756468977606}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468977612, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756468977612}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468977612, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468977612}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468977501, "name": "GET /cashier/deposit/screen-info -> 417", "fullName": "GET /cashier/deposit/screen-info -> 417", "stop": 1756468977613}