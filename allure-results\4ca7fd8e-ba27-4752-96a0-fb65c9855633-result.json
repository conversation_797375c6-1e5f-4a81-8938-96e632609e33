{"uuid": "4ca7fd8e-ba27-4752-96a0-fb65c9855633", "historyId": "42d95ceec434939ff423faf8784b5251", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469002391, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/phoneavailability POST", "stop": 1756469002391}], "attachments": [], "parameters": [], "start": 1756469002148, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 30 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: { phone: '+5500000000' }, failOnStatusCode: false }\")", "stop": 1756469002391}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469002399, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756469002399}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756469002400, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756469002400}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756469002076, "name": "POST /utils/phoneavailability -> 417", "fullName": "POST /utils/phoneavailability -> 417", "stop": 1756469002400}