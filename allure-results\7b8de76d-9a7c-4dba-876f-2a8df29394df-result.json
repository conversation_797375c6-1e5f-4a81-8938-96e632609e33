{"uuid": "7b8de76d-9a7c-4dba-876f-2a8df29394df", "historyId": "132a58b091f9d612bb042fe22ebd6a84", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468989348, "name": "https://platform-api.dev.estrelabet.bet.br/api/home/<USER>", "stop": 1756468989348}], "attachments": [], "parameters": [], "start": 1756468989291, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 24 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756468989348}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468989355, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"platform not recognized\",\"code\":11}: expected 400 to equal 417", "stop": 1756468989355}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468989356, "name": "assert Esperado 417. Re<PERSON>bido: 400. Body: {\"error\":\"platform not recognized\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468989356}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468989240, "name": "GET /home/<USER>", "fullName": "GET /home/<USER>", "stop": 1756468989356}