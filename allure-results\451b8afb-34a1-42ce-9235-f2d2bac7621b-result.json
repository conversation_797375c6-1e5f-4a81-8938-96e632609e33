{"uuid": "451b8afb-34a1-42ce-9235-f2d2bac7621b", "historyId": "31573fcdc03f95e03279f52c9ff13277", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468981420, "name": "https://platform-api.dev.estrelabet.bet.br/api/games/get-real-games GET", "stop": 1756468981420}], "attachments": [], "parameters": [], "start": 1756468981362, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 27 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, qs: { hasDemo: false, language: 'pt-BR', upcomingGames: false }, failOnStatusCode: false }\")", "stop": 1756468981420}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468981430, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756468981430}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468981431, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468981431}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468981292, "name": "GET /games/get-real-games -> 417", "fullName": "GET /games/get-real-games -> 417", "stop": 1756468981431}