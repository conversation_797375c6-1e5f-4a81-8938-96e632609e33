{"uuid": "6cb2ae03-be6e-4f2b-ad70-9c72e77c67f3", "historyId": "76b293fda5ee9be4309e7ffe76fe0700", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468983440, "name": "https://platform-api.dev.estrelabet.bet.br/api/profile/last-played-games GET", "stop": 1756468983440}], "attachments": [], "parameters": [], "start": 1756468983388, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 32 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, failOnStatusCode: false }\")", "stop": 1756468983440}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468983450, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756468983450}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468983450, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468983450}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468983320, "name": "GET /profile/last-played-games -> 417", "fullName": "GET /profile/last-played-games -> 417", "stop": 1756468983450}