{"uuid": "b1fed614-977b-42dc-a660-e184d5b8e425", "historyId": "0325fe843240b31a1d64c867f115052f", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468992949, "name": "https://platform-api.dev.estrelabet.bet.br/api/management/bonusLed POST", "stop": 1756468992949}], "attachments": [], "parameters": [], "start": 1756468992891, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 26 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756468992949}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468992958, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"Invalid bonus led data\",\"code\":11}: expected 400 to equal 417", "stop": 1756468992958}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468992959, "name": "assert Esperado 417. <PERSON><PERSON><PERSON><PERSON>: 400. Body: {\"error\":\"Invalid bonus led data\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468992959}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468992839, "name": "POST /management/bonusLed -> 417", "fullName": "POST /management/bonusLed -> 417", "stop": 1756468992959}