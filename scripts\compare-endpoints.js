const fs=require('fs');
const pm=fs.readFileSync('cypress/support/postman.json','utf8');
const test=fs.readFileSync('cypress/e2e/Backend/api-plataform.cy.js','utf8');

// Collect postman endpoints (regex scan, not JSON.parse)
const pmRe=/"(\/api\/v3\/[^"}]+)"\s*:/g;
const pmSet=new Set();
let m;
while((m=pmRe.exec(pm))){ pmSet.add(m[1]); }

// Collect tested endpoints
const tRe=/url:\s*api\('(\/[^']+)'\)/g;
const tSet=new Set();
while((m=tRe.exec(test))){ tSet.add(m[1]); }

// Normalize to /api/v3 + tested path
const testedFull=new Set(Array.from(tSet, p=>'/api/v3'+p));

// Diff
const notCovered=Array.from(pmSet).filter(ep=>!testedFull.has(ep)).sort();
console.log('Total PM /api/v3 endpoints:', pmSet.size);
console.log('Total tested endpoints:', tSet.size);
const covered=Array.from(pmSet).filter(ep=>testedFull.has(ep)).sort();
console.log('Cobertos ('+covered.length+'):\n'+covered.join('\n'));
console.log('\nNao mapeados ('+notCovered.length+'):\n'+notCovered.join('\n'));

