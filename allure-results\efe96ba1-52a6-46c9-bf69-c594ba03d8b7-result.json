{"uuid": "efe96ba1-52a6-46c9-bf69-c594ba03d8b7", "historyId": "e232fe550bcebbf0e2dd23df6bdf8257", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468987438, "name": "https://platform-api.dev.estrelabet.bet.br/api/games/get-lobby-group-games GET", "stop": 1756468987438}], "attachments": [], "parameters": [], "start": 1756468986745, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 34 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, qs: { language: 'pt-BR' }, failOnStatusCode: false }\")", "stop": 1756468987438}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468987444, "name": "Assert skipped Esperado 417. Recebido: 500. Body: {\"error\":\"get lobby games group: request failed with status 500\",\"code\":10}: expected 500 to equal 417", "stop": 1756468987444}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 500}, {"name": "expected", "value": 417}], "start": 1756468987445, "name": "assert Esperado 417. Recebido: 500. Body: {\"error\":\"get lobby games group: request failed with status 500\",\"code\":10}: expected **500** to equal **417**", "stop": 1756468987445}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468986671, "name": "GET /games/get-lobby-group-games -> 417", "fullName": "GET /games/get-lobby-group-games -> 417", "stop": 1756468987445}