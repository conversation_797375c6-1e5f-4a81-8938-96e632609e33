{"uuid": "ce53b69f-6f53-4d07-b768-ebc8b105811c", "historyId": "129c68fb255491285b9a6c79f0de1f81", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467559578, "name": "https://platform-api.dev.estrelabet.bet.br/api/cashier/deposit/wallet-withdrawal POST", "stop": 1756467559578}], "attachments": [], "parameters": [], "start": 1756467559515, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 40 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: { amount: 1, method: 'pix' }, failOnStatusCode: false }\")", "stop": 1756467559578}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467559587, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"invalid session\",\"code\":11}: expected 400 to equal 417", "stop": 1756467559587}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467559587, "name": "assert Esperado 417. <PERSON><PERSON>bido: 400. Body: {\"error\":\"invalid session\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467559587}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467559446, "name": "POST /cashier/deposit/wallet-withdrawal -> 417", "fullName": "POST /cashier/deposit/wallet-withdrawal -> 417", "stop": 1756467559588}