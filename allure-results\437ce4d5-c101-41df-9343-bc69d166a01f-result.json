{"uuid": "437ce4d5-c101-41df-9343-bc69d166a01f", "historyId": "fd392880172d9c3b4d712b57025f2bfd", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468993981, "name": "https://platform-api.dev.estrelabet.bet.br/api/management/stories POST", "stop": 1756468993981}], "attachments": [], "parameters": [], "start": 1756468993877, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 25 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756468993981}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468994144, "name": "Assert skipped Esperado 417. Recebido: 403. Body: \"<!DOCTYPE html>\\n<!--[if lt IE 7]> <html class=\\\"no-js ie6 oldie\\\" lang=\\\"en-US\\\"> <![endif]-->\\n<!--[if IE 7]>    <html class=\\\"no-js ie7 oldie\\\" lang=\\\"en-US\\\"> <![endif]-->\\n<!--[if IE 8]>    <html class=\\\"no-js ie8 oldie\\\" lang=\\\"en-US\\\"> <![endif]-->\\n<!--[if gt IE 8]><!--> <html class=\\\"no-js\\\" lang=\\\"en-US\\\"> <!--<![endif]-->\\n<head>\\n<title>Attention Required! | Cloudflare</title>\\n<meta charset=\\\"UTF-8\\\" />\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=UTF-8\\\" />\\n<meta http-equiv=\\\"X-UA-Compatible\\\" content=\\\"IE=Edge\\\" />\\n<meta name=\\\"robots\\\" content=\\\"noindex, nofollow\\\" />\\n<meta name=\\\"viewport\\\" content=\\\"width=device-width,initial-scale=1\\\" />\\n<link rel=\\\"stylesheet\\\" id=\\\"cf_styles-css\\\" href=\\\"/cdn-cgi/styles/cf.errors.css\\\" />\\n<!--[if lt IE 9]><link rel=\\\"stylesheet\\\" id='cf_styles-ie-css' href=\\\"/cdn-cgi/styles/cf.errors.ie.css\\\" /><![endif]-->\\n<style>body{margin:0;padding:0}</style>\\n\\n\\n<!--[if gte IE 10]><!-->\\n<script>\\n  if (!navigator.cookieEnabled) {\\n    window.addEventListener('DOMContentLoaded', function () {\\n      var cookieEl = document.getElementById('cookie-alert');\\n      cookieEl.style.display = 'block';\\n    })\\n  }\\n</script>\\n<!--<![endif]-->\\n\\n\\n</head>\\n<body>\\n  <div id=\\\"cf-wrapper\\\">\\n    <div class=\\\"cf-alert cf-alert-error cf-cookie-error\\\" id=\\\"cookie-alert\\\" data-translate=\\\"enable_cookies\\\">Please enable cookies.</div>\\n    <div id=\\\"cf-error-details\\\" class=\\\"cf-error-details-wrapper\\\">\\n      <div class=\\\"cf-wrapper cf-header cf-error-overview\\\">\\n        <h1 data-translate=\\\"block_headline\\\">Sorry, you have been blocked</h1>\\n        <h2 class=\\\"cf-subheadline\\\"><span data-translate=\\\"unable_to_access\\\">You are unable to access</span> estrelabet.bet.br</h2>\\n      </div><!-- /.header -->\\n\\n      <div class=\\\"cf-section cf-highlight\\\">\\n        <div class=\\\"cf-wrapper\\\">\\n          <div class=\\\"cf-screenshot-container cf-screenshot-full\\\">\\n            \\n              <span class=\\\"cf-no-screenshot error\\\"></span>\\n            \\n          </div>\\n        </div>\\n      </div><!-- /.captcha-container -->\\n\\n      <div class=\\\"cf-section cf-wrapper\\\">\\n        <div class=\\\"cf-columns two\\\">\\n          <div class=\\\"cf-column\\\">\\n            <h2 data-translate=\\\"blocked_why_headline\\\">Why have I been blocked?</h2>\\n\\n            <p data-translate=\\\"blocked_why_detail\\\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>\\n          </div>\\n\\n          <div class=\\\"cf-column\\\">\\n            <h2 data-translate=\\\"blocked_resolve_headline\\\">What can I do to resolve this?</h2>\\n\\n            <p data-translate=\\\"blocked_resolve_detail\\\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>\\n          </div>\\n        </div>\\n      </div><!-- /.section -->\\n\\n      <div class=\\\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\\\">\\n  <p class=\\\"text-13\\\">\\n    <span class=\\\"cf-footer-item sm:block sm:mb-1\\\">Cloudflare Ray ID: <strong class=\\\"font-semibold\\\">976be760f9844360</strong></span>\\n    <span class=\\\"cf-footer-separator sm:hidden\\\">&bull;</span>\\n    <span id=\\\"cf-footer-item-ip\\\" class=\\\"cf-footer-item hidden sm:block sm:mb-1\\\">\\n      Your IP:\\n      <button type=\\\"button\\\" id=\\\"cf-footer-ip-reveal\\\" class=\\\"cf-footer-ip-reveal-btn\\\">Click to reveal</button>\\n      <span class=\\\"hidden\\\" id=\\\"cf-footer-ip\\\">***************</span>\\n      <span class=\\\"cf-footer-separator sm:hidden\\\">&bull;</span>\\n    </span>\\n    <span class=\\\"cf-footer-item sm:block sm:mb-1\\\"><span>Performance &amp; security by</span> <a rel=\\\"noopener noreferrer\\\" href=\\\"https://www.cloudflare.com/5xx-error-landing\\\" id=\\\"brand_link\\\" target=\\\"_blank\\\">Cloudflare</a></span>\\n    \\n  </p>\\n  <script>(function(){function d(){var b=a.getElementById(\\\"cf-footer-item-ip\\\"),c=a.getElementById(\\\"cf-footer-ip-reveal\\\");b&&\\\"classList\\\"in b&&(b.classList.remove(\\\"hidden\\\"),c.addEventListener(\\\"click\\\",function(){c.classList.add(\\\"hidden\\\");a.getElementById(\\\"cf-footer-ip\\\").classList.remove(\\\"hidden\\\")}))}var a=document;document.addEventListener&&a.addEventListener(\\\"DOMContentLoaded\\\",d)})();</script>\\n</div><!-- /.error-footer -->\\n\\n\\n    </div><!-- /#cf-error-details -->\\n  </div><!-- /#cf-wrapper -->\\n\\n  <script>\\n  window._cf_translation = {};\\n  \\n  \\n</script>\\n\\n<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\\\"window.__CF$cv$params={r:'976be760f9844360',t:'MTc1NjQ2ODk5Mi4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\\\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>\\n</html>\\n\": expected 403 to equal 417", "stop": 1756468994144}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 403}, {"name": "expected", "value": 417}], "start": 1756468994145, "name": "assert Esperado 417. Recebido: 403. Body: \"<!DOCTYPE html>\\n<!--[if lt IE 7]> <html class=\\\"no-js ie6 oldie\\\" lang=\\\"en-US\\\"> <![endif]-->\\n<!--[if IE 7]>    <html class=\\\"no-js ie7 oldie\\\" lang=\\\"en-US\\\"> <![endif]-->\\n<!--[if IE 8]>    <html class=\\\"no-js ie8 oldie\\\" lang=\\\"en-US\\\"> <![endif]-->\\n<!--[if gt IE 8]><!--> <html class=\\\"no-js\\\" lang=\\\"en-US\\\"> <!--<![endif]-->\\n<head>\\n<title>Attention Required! | Cloudflare</title>\\n<meta charset=\\\"UTF-8\\\" />\\n<meta http-equiv=\\\"Content-Type\\\" content=\\\"text/html; charset=UTF-8\\\" />\\n<meta http-equiv=\\\"X-UA-Compatible\\\" content=\\\"IE=Edge\\\" />\\n<meta name=\\\"robots\\\" content=\\\"noindex, nofollow\\\" />\\n<meta name=\\\"viewport\\\" content=\\\"width=device-width,initial-scale=1\\\" />\\n<link rel=\\\"stylesheet\\\" id=\\\"cf_styles-css\\\" href=\\\"/cdn-cgi/styles/cf.errors.css\\\" />\\n<!--[if lt IE 9]><link rel=\\\"stylesheet\\\" id='cf_styles-ie-css' href=\\\"/cdn-cgi/styles/cf.errors.ie.css\\\" /><![endif]-->\\n<style>body{margin:0;padding:0}</style>\\n\\n\\n<!--[if gte IE 10]><!-->\\n<script>\\n  if (!navigator.cookieEnabled) {\\n    window.addEventListener('DOMContentLoaded', function () {\\n      var cookieEl = document.getElementById('cookie-alert');\\n      cookieEl.style.display = 'block';\\n    })\\n  }\\n</script>\\n<!--<![endif]-->\\n\\n\\n</head>\\n<body>\\n  <div id=\\\"cf-wrapper\\\">\\n    <div class=\\\"cf-alert cf-alert-error cf-cookie-error\\\" id=\\\"cookie-alert\\\" data-translate=\\\"enable_cookies\\\">Please enable cookies.</div>\\n    <div id=\\\"cf-error-details\\\" class=\\\"cf-error-details-wrapper\\\">\\n      <div class=\\\"cf-wrapper cf-header cf-error-overview\\\">\\n        <h1 data-translate=\\\"block_headline\\\">Sorry, you have been blocked</h1>\\n        <h2 class=\\\"cf-subheadline\\\"><span data-translate=\\\"unable_to_access\\\">You are unable to access</span> estrelabet.bet.br</h2>\\n      </div><!-- /.header -->\\n\\n      <div class=\\\"cf-section cf-highlight\\\">\\n        <div class=\\\"cf-wrapper\\\">\\n          <div class=\\\"cf-screenshot-container cf-screenshot-full\\\">\\n            \\n              <span class=\\\"cf-no-screenshot error\\\"></span>\\n            \\n          </div>\\n        </div>\\n      </div><!-- /.captcha-container -->\\n\\n      <div class=\\\"cf-section cf-wrapper\\\">\\n        <div class=\\\"cf-columns two\\\">\\n          <div class=\\\"cf-column\\\">\\n            <h2 data-translate=\\\"blocked_why_headline\\\">Why have I been blocked?</h2>\\n\\n            <p data-translate=\\\"blocked_why_detail\\\">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>\\n          </div>\\n\\n          <div class=\\\"cf-column\\\">\\n            <h2 data-translate=\\\"blocked_resolve_headline\\\">What can I do to resolve this?</h2>\\n\\n            <p data-translate=\\\"blocked_resolve_detail\\\">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>\\n          </div>\\n        </div>\\n      </div><!-- /.section -->\\n\\n      <div class=\\\"cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300\\\">\\n  <p class=\\\"text-13\\\">\\n    <span class=\\\"cf-footer-item sm:block sm:mb-1\\\">Cloudflare Ray ID: <strong class=\\\"font-semibold\\\">976be760f9844360</strong></span>\\n    <span class=\\\"cf-footer-separator sm:hidden\\\">&bull;</span>\\n    <span id=\\\"cf-footer-item-ip\\\" class=\\\"cf-footer-item hidden sm:block sm:mb-1\\\">\\n      Your IP:\\n      <button type=\\\"button\\\" id=\\\"cf-footer-ip-reveal\\\" class=\\\"cf-footer-ip-reveal-btn\\\">Click to reveal</button>\\n      <span class=\\\"hidden\\\" id=\\\"cf-footer-ip\\\">***************</span>\\n      <span class=\\\"cf-footer-separator sm:hidden\\\">&bull;</span>\\n    </span>\\n    <span class=\\\"cf-footer-item sm:block sm:mb-1\\\"><span>Performance &amp; security by</span> <a rel=\\\"noopener noreferrer\\\" href=\\\"https://www.cloudflare.com/5xx-error-landing\\\" id=\\\"brand_link\\\" target=\\\"_blank\\\">Cloudflare</a></span>\\n    \\n  </p>\\n  <script>(function(){function d(){var b=a.getElementById(\\\"cf-footer-item-ip\\\"),c=a.getElementById(\\\"cf-footer-ip-reveal\\\");b&&\\\"classList\\\"in b&&(b.classList.remove(\\\"hidden\\\"),c.addEventListener(\\\"click\\\",function(){c.classList.add(\\\"hidden\\\");a.getElementById(\\\"cf-footer-ip\\\").classList.remove(\\\"hidden\\\")}))}var a=document;document.addEventListener&&a.addEventListener(\\\"DOMContentLoaded\\\",d)})();</script>\\n</div><!-- /.error-footer -->\\n\\n\\n    </div><!-- /#cf-error-details -->\\n  </div><!-- /#cf-wrapper -->\\n\\n  <script>\\n  window._cf_translation = {};\\n  \\n  \\n</script>\\n\\n<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\\\"window.__CF$cv$params={r:'976be760f9844360',t:'MTc1NjQ2ODk5Mi4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\\\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>\\n</html>\\n\": expected **403** to equal **417**", "stop": 1756468994145}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468993778, "name": "POST /management/stories -> 417", "fullName": "POST /management/stories -> 417", "stop": 1756468994145}