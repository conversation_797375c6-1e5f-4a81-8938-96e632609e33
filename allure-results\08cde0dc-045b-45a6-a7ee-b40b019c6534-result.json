{"uuid": "08cde0dc-045b-45a6-a7ee-b40b019c6534", "historyId": "d9c38c2f8ae264bb1b87be3101cc0dcc", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468988356, "name": "https://platform-api.dev.estrelabet.bet.br/api/games/search-games GET", "stop": 1756468988357}], "attachments": [], "parameters": [], "start": 1756468988125, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 25 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, qs: { language: 'pt-BR', search: 'game' }, failOnStatusCode: false }\")", "stop": 1756468988357}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468988362, "name": "Assert skipped Esperado 417. Recebido: 500. Body: {\"error\":\"get lobby games group: request failed with status 500\",\"code\":10}: expected 500 to equal 417", "stop": 1756468988362}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 500}, {"name": "expected", "value": 417}], "start": 1756468988362, "name": "assert Esperado 417. Recebido: 500. Body: {\"error\":\"get lobby games group: request failed with status 500\",\"code\":10}: expected **500** to equal **417**", "stop": 1756468988362}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468988072, "name": "GET /games/search-games -> 417", "fullName": "GET /games/search-games -> 417", "stop": 1756468988363}