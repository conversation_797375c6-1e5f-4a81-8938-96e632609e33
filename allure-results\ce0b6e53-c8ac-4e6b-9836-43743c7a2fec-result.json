{"uuid": "ce0b6e53-c8ac-4e6b-9836-43743c7a2fec", "historyId": "49548c3272252514cb24c1754fb1990d", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467572989, "name": "https://platform-api.dev.estrelabet.bet.br/api/profile/third-party-kyc POST", "stop": 1756467572989}], "attachments": [], "parameters": [], "start": 1756467572934, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 30 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756467572989}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467572997, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756467572997}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467572997, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467572997}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467572883, "name": "POST /profile/third-party-kyc -> 417", "fullName": "POST /profile/third-party-kyc -> 417", "stop": 1756467572997}