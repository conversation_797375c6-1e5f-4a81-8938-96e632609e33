{"uuid": "5b190f31-15fd-41db-ab06-e7283e0f3e08", "historyId": "615952dbed752bf77a5ca8e92e8e1947", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467561512, "name": "https://platform-api.dev.estrelabet.bet.br/api/profile/send-auth-code POST", "stop": 1756467561512}], "attachments": [], "parameters": [], "start": 1756467561457, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 29 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: { channel: 'email' }, failOnStatusCode: false }\")", "stop": 1756467561512}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467561520, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756467561520}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467561521, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467561521}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467561395, "name": "POST /profile/send-auth-code -> 417", "fullName": "POST /profile/send-auth-code -> 417", "stop": 1756467561521}