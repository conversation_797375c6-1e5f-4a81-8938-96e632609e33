/// <reference types="cypress" />

import loc from '../../support/locators'
import { Utility } from "../../support/utility"
import '@cypress/grep'
import { blockTrackers } from '../../support/blockTrackers'
import '@shelex/cypress-allure-plugin'

const url = new Utility().getBaseUrl();

describe('Deve abrir a página de cassino', () => {

    it('Valida elementos do footer', () => {
        blockTrackers();
        cy.visit(url)
        // "Aposte"
        cy.contains("span", "Aposte").should("be.visible");
        // 19/08/2025 mudaram o locator
        //cy.validateText('.StaticFooter_footer-links-section__0Fi_h', 'Aposte')

        cy.validateText('[href="https://www.estrelabet.bet.br/aposta-esportiva"]', 'Apostas esportivas')
        cy.validateText('[href="https://www.estrelabet.bet.br/gameplay/fortune-tiger"]', 'Fortune Tiger')
        cy.validateText('[href="https://www.estrelabet.bet.br/gameplay/fortune-rabbit"]', 'Fortune Rabbit')
        cy.validateText('[href="https://play.google.com/store/apps/details?id=co.br.bet.estrelabet.app&pli=1"]', 'App de Apostas')
        // "Links úteis"
        //cy.validateText('.StaticFooter_footer-links-header__U2WL_', 'Links úteis')
        cy.contains("span", "Links úteis").should("be.visible");

        cy.validateText('[href="https://comunidade.estrelabet.com/"]', 'Comunidade')
        cy.validateText('[href="https://www.estrelabet.bet.br/pb/offers"]', 'Ofertas')
        cy.validateText('[href="https://www.estrelabet.bet.br/page/responsible-gaming"] > span:eq(0)', 'Jogo responsável')
        cy.validateText('[href="https://blog.estrelabet.com/"]', 'Blog')

        // "Regras"
        cy.contains("span", "Regras").should("be.visible");
        //cy.validateText('.StaticFooter_footer-links-header__U2WL_', 'Regras')

        cy.validateText('[href="https://www.estrelabet.bet.br/pb/politica/termos-e-condicoes"]', 'Termos e Condições Gerais') //[href="https://www.estrelabet.bet.br/politica/termos-e-condicoes
        cy.validateText('[href="https://www.estrelabet.bet.br/page/responsible-gaming"] > span:eq(1)', 'Jogo responsável')
        cy.validateText('[href="https://www.estrelabet.bet.br/policy/sports-betting-rules"]', 'Regras de apostas esportivas')
        cy.validateText('[href="https://www.estrelabet.bet.br/policy/bonus-rules"]', 'Termos e condições gerais de bônus')
        cy.validateText('[href="https://www.estrelabet.bet.br/policy/privacy-policy"]', 'Política de privacidade')

        // "Suporte"
        //cy.validateText('.StaticFooter_footer-links-header__U2WL_', 'Suporte')
        cy.contains("span", "Suporte").should("be.visible");

        cy.validateText('[href="https://estrelabet.zendesk.com/hc"]', 'Central de ajuda')
        cy.validateText('[href="tel:0800 000 4546"]', '0800 000 4546')
        cy.validateText('[href="mailto:<EMAIL>"]', '<EMAIL>')

        // "Outros"
        //cy.validateText('.StaticFooter_footer-links-header__U2WL_', 'Outros')
        cy.contains("span", "Outros").should("be.visible");

        cy.validateText('[href="https://estrela-bet.atlassian.net/helpcenter/ouvidoria/"]', 'Ouvidoria')
        cy.validateText('[href="https://consumidor2.procon.sp.gov.br/login"]', 'Procon')
        cy.validateText('[href="https://www.planalto.gov.br/ccivil_03/leis/l8078compilado.htm"]', 'Código de Defesa do Consumidor')

    });
});