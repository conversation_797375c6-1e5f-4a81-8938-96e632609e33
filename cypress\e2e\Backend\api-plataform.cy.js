/// <reference types="cypress" />

import { Utility } from '../../support/utility'

const util = new Utility()
const baseUrl = 'https://platform-api.dev.estrelabet.bet.br/api'

// Helper para compor URL completa com segurança
const api = (path) => `${baseUrl}${path}`

// Headers comuns. Preencha conforme necessário para o seu backend.
const commonHeaders = () => ({
  'EB-Client-App': 'swagger',
  'X-API-Key': '',
  'X-Timestamp': '',
  'X-Nonce': '',
  'X-Signature': '',
})

// Alguns endpoints exigem sessão
const withSession = (headers = {}) => ({
  ...headers,
  'EB-PS-Session-Id': Cypress.env('PS_SESSION_ID') || '', // defina via --env PS_SESSION_ID=<valor>
})

describe('Platform API - smoke 417', () => {

// Se baseUrl tiver /v3, validamos 417. <PERSON><PERSON><PERSON> contr<PERSON>, logamos o status/body sem falhar o teste
Cypress.on('fail', (err) => {
  const isV3 = baseUrl.includes('/v3')
  const is417Expectation = typeof err.message === 'string' && err.message.includes('Esperado 417')
  if (!isV3 && is417Expectation) {
    Cypress.log({ name: 'Assert skipped', message: err.message })
    return false
  }
  throw err
})

  it('GET /cashier/deposit/account -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/cashier/deposit/account'),
      headers: withSession(commonHeaders()),
      qs: {
        deposit: '',
        verified: true,
      },
      failOnStatusCode: false,
    }).then((res) => {
      expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417)
    })
  })

  it('GET /cashier/deposit/queued-notifications -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/cashier/deposit/queued-notifications'),
      headers: commonHeaders(),
      failOnStatusCode: false,
    }).then((res) => {
      expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417)
    })
  })

  it('DELETE /cashier/deposit/queued-notifications -> 417', () => {
    cy.api({
      method: 'DELETE',
      url: api('/cashier/deposit/queued-notifications'),
      headers: commonHeaders(),
      failOnStatusCode: false,
    }).then((res) => {
      expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417)
    })
  })

  it('GET /cashier/deposit/screen-info -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/cashier/deposit/screen-info'),
      headers: withSession(commonHeaders()),
      qs: { platform: 'web' },
      failOnStatusCode: false,
    }).then((res) => {
      expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417)
    })
  })

  it('POST /cashier/deposit/wallet-payment -> 417', () => {
    cy.api({
      method: 'POST',
      url: api('/cashier/deposit/wallet-payment'),
      headers: withSession(commonHeaders()),
      body: {
        amount: 1,
        method: 'pix',
      },
      failOnStatusCode: false,
    }).then((res) => {
      expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417)
    })
  })

  it('POST /cashier/deposit/queue-notification -> 417', () => {
    cy.api({
      method: 'POST',
      url: api('/cashier/deposit/queue-notification'),
      headers: withSession(commonHeaders()),
      body: {
        transactionId: 'test-transaction',
        status: 'ok',
      },
      failOnStatusCode: false,
    }).then((res) => {
      expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417)
    })
  })

  it('DELETE /games/cache/all -> 417', () => {
    cy.api({
      method: 'DELETE',
      url: api('/games/cache/all'),
      headers: commonHeaders(),
      failOnStatusCode: false,
    }).then((res) => {
      expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417)
    })
  })

  it('GET /games/get-free-games -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/games/get-free-games'),
      headers: commonHeaders(),
      qs: {
        gameSymbol: '',
        language: 'pt-BR',
        upcomingGames: false,
      },
      failOnStatusCode: false,
    }).then((res) => {
      expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417)
    })
  })

  it('GET /games/get-group-games -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/games/get-group-games'),
      headers: commonHeaders(),
      qs: {
        gameCodes: '1,2,3',
        language: 'pt-BR',
      },
      failOnStatusCode: false,
    }).then((res) => {
      expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417)
    })
  })

  it('GET /games/get-list-group -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/games/get-list-group'),
      headers: commonHeaders(),
      failOnStatusCode: false,
    }).then((res) => {
      expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417)
    })
  })

  it('POST /cashier/deposit/wallet-withdrawal -> 417', () => {
    cy.api({
      method: 'POST',
      url: api('/cashier/deposit/wallet-withdrawal'),
      headers: withSession(commonHeaders()),
      body: { amount: 1, method: 'pix' },
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /games/get-real-games -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/games/get-real-games'),
      headers: commonHeaders(),
      qs: { hasDemo: false, language: 'pt-BR', upcomingGames: false },
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /login -> 417', () => {
    cy.api({
      method: 'POST',
      url: api('/login'),
      headers: commonHeaders(),
      body: { email: '<EMAIL>', password: 'Password123' },
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /login/logout -> 417', () => {
    cy.api({
      method: 'POST',
      url: api('/login/logout'),
      headers: withSession(commonHeaders()),
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /login/social -> 417', () => {
    cy.api({
      method: 'POST',
      url: api('/login/social'),
      headers: commonHeaders(),
      body: { provider: 'google', accessToken: 'token' },
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /profile -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/profile'),
      headers: withSession(commonHeaders()),
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /profile/balance -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/profile/balance'),
      headers: withSession(commonHeaders()),
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /profile/kyc-url -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/profile/kyc-url'),
      headers: withSession(commonHeaders()),
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /profile/last-played-games -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/profile/last-played-games'),
      headers: withSession(commonHeaders()),
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /profile/send-auth-code -> 417', () => {
    cy.api({
      method: 'POST',
      url: api('/profile/send-auth-code'),
      headers: withSession(commonHeaders()),
      body: { channel: 'email' },
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /profile/verify-auth-code -> 417', () => {
    cy.api({
      method: 'POST',
      url: api('/profile/verify-auth-code'),
      headers: withSession(commonHeaders()),
      body: { code: '123456' },
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /promotions -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/promotions'),
      headers: commonHeaders(),
      qs: { languageId: 'pt', hiddenPromotion: false },
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /promotions/{promotionId} -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/promotions/1'),
      headers: commonHeaders(),
      qs: { languageId: 'pt', hiddenPromotion: false },
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /utils/address -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/utils/address'),
      headers: { ...commonHeaders(), cep: '01001000' },
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  // Games extras
  it('GET /games/get-lobby-group-games -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/games/get-lobby-group-games'),
      headers: commonHeaders(),
      qs: { language: 'pt-BR' },
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /games/get-lobby-group-games-paginated -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/games/get-lobby-group-games-paginated'),
      headers: commonHeaders(),
      qs: { language: 'pt-BR', page: 1, pageSize: 20 },
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /games/search-games -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/games/search-games'),
      headers: commonHeaders(),
      qs: { language: 'pt-BR', search: 'game' },
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  // Home
  it('GET /home -> 417', () => {
    cy.api({
      method: 'GET',
      url: api('/home'),
      headers: commonHeaders(),
      qs: { platform: 'web' },
      failOnStatusCode: false,
    }).then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /home/<USER>', () => {
    cy.api({ method: 'GET', url: api('/home/<USER>'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /home/<USER>', () => {
    cy.api({ method: 'GET', url: api('/home/<USER>'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /home/<USER>', () => {
    cy.api({ method: 'GET', url: api('/home/<USER>'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /home/<USER>', () => {
    cy.api({ method: 'GET', url: api('/home/<USER>'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /home/<USER>', () => {
    cy.api({ method: 'GET', url: api('/home/<USER>'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /home/<USER>', () => {
    cy.api({ method: 'GET', url: api('/home/<USER>'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /home/<USER>', () => {
    cy.api({ method: 'GET', url: api('/home/<USER>'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /home/<USER>', () => {
    cy.api({ method: 'GET', url: api('/home/<USER>'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /home/<USER>', () => {
    cy.api({ method: 'GET', url: api('/home/<USER>'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })


  // Management
  it('POST /management/bonusLed -> 417', () => {
    cy.api({ method: 'POST', url: api('/management/bonusLed'), headers: commonHeaders(), body: {}, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /management/matches -> 417', () => {
    cy.api({ method: 'POST', url: api('/management/matches'), headers: commonHeaders(), body: {}, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /management/sportsBetting -> 417', () => {
    cy.api({ method: 'POST', url: api('/management/sportsBetting'), headers: commonHeaders(), body: {}, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /management/stories -> 417', () => {
    cy.api({ method: 'POST', url: api('/management/stories'), headers: commonHeaders(), body: {}, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /management/top-scorers -> 417', () => {
    cy.api({ method: 'POST', url: api('/management/top-scorers'), headers: commonHeaders(), body: {}, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  // Profile extras
  it('GET /profile/kyc-details -> 417', () => {
    cy.api({ method: 'GET', url: api('/profile/kyc-details'), headers: withSession(commonHeaders()), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /profile/third-party-kyc -> 417', () => {
    cy.api({ method: 'POST', url: api('/profile/third-party-kyc'), headers: withSession(commonHeaders()), body: {}, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /profile/update-profile -> 417', () => {
    cy.api({ method: 'POST', url: api('/profile/update-profile'), headers: withSession(commonHeaders()), body: {}, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  // Reviews/Stories
  it('GET /reviews -> 417', () => {
    cy.api({ method: 'GET', url: api('/reviews'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /reviews/export -> 417', () => {
    cy.api({ method: 'GET', url: api('/reviews/export'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /stories -> 417', () => {
    cy.api({ method: 'GET', url: api('/stories'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  // Utils e Cashier extras
  it('POST /utils -> 417', () => {
    cy.api({ method: 'POST', url: api('/utils'), headers: commonHeaders(), body: {}, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /utils/active-bonus -> 417', () => {
    cy.api({ method: 'GET', url: api('/utils/active-bonus'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /utils/bonus -> 417', () => {
    cy.api({ method: 'GET', url: api('/utils/bonus'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /utils/claim-bonus -> 417', () => {
    cy.api({ method: 'GET', url: api('/utils/claim-bonus'), headers: commonHeaders(), qs: { accept: true }, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /utils/drop-bonus -> 417', () => {
    cy.api({ method: 'GET', url: api('/utils/drop-bonus'), headers: commonHeaders(), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /utils/phoneavailability -> 417', () => {
    cy.api({ method: 'POST', url: api('/utils/phoneavailability'), headers: commonHeaders(), body: { phone: '+5500000000' }, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /utils/referral-eligible -> 417', () => {
    cy.api({ method: 'GET', url: api('/utils/referral-eligible'), headers: withSession(commonHeaders()), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /utils/social -> 417', () => {
    cy.api({ method: 'POST', url: api('/utils/social'), headers: commonHeaders(), body: {}, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('GET /utils/user-acknowledgement -> 417', () => {
    cy.api({ method: 'GET', url: api('/utils/user-acknowledgement'), headers: withSession(commonHeaders()), failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /utils/user-policy-details -> 417', () => {
    cy.api({ method: 'POST', url: api('/utils/user-policy-details'), headers: withSession(commonHeaders()), body: {}, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /utils/validatecpf -> 417', () => {
    cy.api({ method: 'POST', url: api('/utils/validatecpf'), headers: commonHeaders(), body: { cpf: '00000000000' }, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /utils/validateemail -> 417', () => {
    cy.api({ method: 'POST', url: api('/utils/validateemail'), headers: commonHeaders(), body: { email: '<EMAIL>' }, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  it('POST /cashier/deposit/update-bank-account -> 417', () => {
    cy.api({ method: 'POST', url: api('/cashier/deposit/update-bank-account'), headers: withSession(commonHeaders()), body: {}, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

  // Login extra
  it('POST /login/social/integration -> 417', () => {
    cy.api({ method: 'POST', url: api('/login/social/integration'), headers: commonHeaders(), body: {}, failOnStatusCode: false })
      .then((res) => { expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417) })
  })

})

