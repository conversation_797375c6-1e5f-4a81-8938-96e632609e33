{"uuid": "f2329400-626c-4da3-bcc6-8e4040a9cd5a", "historyId": "d05e7ad76c4452d540ab7ba736262e31", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467567346, "name": "https://platform-api.dev.estrelabet.bet.br/api/home/<USER>", "stop": 1756467567346}], "attachments": [], "parameters": [], "start": 1756467566537, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 20 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756467567346}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467566474, "name": "GET /home/<USER>", "fullName": "GET /home/<USER>", "stop": 1756467567359}