{"uuid": "03d75bbb-23fd-4e83-ab51-49343b9589f5", "historyId": "8eb53d1808ef58088fe3b153f81f08db", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467562102, "name": "https://platform-api.dev.estrelabet.bet.br/api/promotions GET", "stop": 1756467562102}], "attachments": [], "parameters": [], "start": 1756467561847, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 17 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, qs: { languageId: 'pt', hiddenPromotion: false }, failOnStatusCode: false }\")", "stop": 1756467562102}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467562108, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 424. Body: {\"error\":\"third-party failed\",\"code\":58}: expected 424 to equal 417", "stop": 1756467562108}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 424}, {"name": "expected", "value": 417}], "start": 1756467562108, "name": "assert Esperado 417. <PERSON><PERSON>bido: 424. Body: {\"error\":\"third-party failed\",\"code\":58}: expected **424** to equal **417**", "stop": 1756467562108}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467561796, "name": "GET /promotions -> 417", "fullName": "GET /promotions -> 417", "stop": 1756467562108}