{"uuid": "8c6b22a7-2d36-4c75-a9af-796cd6f5f6e3", "historyId": "5631df480280894f336a111309397f14", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467556359, "name": "https://platform-api.dev.estrelabet.bet.br/api/cashier/deposit/queue-notification POST", "stop": 1756467556359}], "attachments": [], "parameters": [], "start": 1756467556305, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 41 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: { transactionId: 'test-transaction', status: 'ok' }, failOnStatusCode: false }\")", "stop": 1756467556359}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467556366, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"invalid session\",\"code\":11}: expected 400 to equal 417", "stop": 1756467556366}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467556367, "name": "assert Esperado 417. <PERSON><PERSON>bido: 400. Body: {\"error\":\"invalid session\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467556367}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467556255, "name": "POST /cashier/deposit/queue-notification -> 417", "fullName": "POST /cashier/deposit/queue-notification -> 417", "stop": 1756467556367}