{"uuid": "2d53a37c-71cd-425d-8480-2949101fca61", "historyId": "bf2d569adea619dc78e3d1ce794f22a6", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468991139, "name": "https://platform-api.dev.estrelabet.bet.br/api/home/<USER>", "stop": 1756468991139}], "attachments": [], "parameters": [], "start": 1756468991077, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 19 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756468991139}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468991147, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"platform not recognized\",\"code\":11}: expected 400 to equal 417", "stop": 1756468991147}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468991147, "name": "assert Esperado 417. Re<PERSON>bido: 400. Body: {\"error\":\"platform not recognized\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468991147}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468991009, "name": "GET /home/<USER>", "fullName": "GET /home/<USER>", "stop": 1756468991147}