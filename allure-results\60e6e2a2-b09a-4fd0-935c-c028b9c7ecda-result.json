{"uuid": "60e6e2a2-b09a-4fd0-935c-c028b9c7ecda", "historyId": "7e86cf88377293607b4e4302723ea000", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467561080, "name": "https://platform-api.dev.estrelabet.bet.br/api/profile/kyc-url GET", "stop": 1756467561080}], "attachments": [], "parameters": [], "start": 1756467561023, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 22 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, failOnStatusCode: false }\")", "stop": 1756467561080}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467561093, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756467561093}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467561094, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467561094}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467560963, "name": "GET /profile/kyc-url -> 417", "fullName": "GET /profile/kyc-url -> 417", "stop": 1756467561094}