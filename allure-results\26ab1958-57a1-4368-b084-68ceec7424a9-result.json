{"uuid": "26ab1958-57a1-4368-b084-68ceec7424a9", "historyId": "8d7dda76f23e919435de1c4dce53c73f", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467567766, "name": "https://platform-api.dev.estrelabet.bet.br/api/home/<USER>", "stop": 1756467567766}], "attachments": [], "parameters": [], "start": 1756467567710, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 20 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756467567766}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467567775, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"platform not recognized\",\"code\":11}: expected 400 to equal 417", "stop": 1756467567775}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467567775, "name": "assert Esperado 417. Re<PERSON>bido: 400. Body: {\"error\":\"platform not recognized\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467567775}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467567663, "name": "GET /home/<USER>", "fullName": "GET /home/<USER>", "stop": 1756467567775}