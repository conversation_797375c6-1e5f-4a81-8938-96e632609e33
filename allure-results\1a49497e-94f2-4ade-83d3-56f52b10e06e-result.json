{"uuid": "1a49497e-94f2-4ade-83d3-56f52b10e06e", "historyId": "e9b7b7eece96c524ba4e4a72e78c29da", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467576760, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils POST", "stop": 1756467576760}], "attachments": [], "parameters": [], "start": 1756467575931, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 12 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756467576760}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467576776, "name": "Assert skipped Esperado 417. Recebido: 500. Body: {\"error\":\"internal server error\",\"code\":10}: expected 500 to equal 417", "stop": 1756467576776}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 500}, {"name": "expected", "value": 417}], "start": 1756467576776, "name": "assert Esperado 417. Recebido: 500. Body: {\"error\":\"internal server error\",\"code\":10}: expected **500** to equal **417**", "stop": 1756467576776}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467575875, "name": "POST /utils -> 417", "fullName": "POST /utils -> 417", "stop": 1756467576776}