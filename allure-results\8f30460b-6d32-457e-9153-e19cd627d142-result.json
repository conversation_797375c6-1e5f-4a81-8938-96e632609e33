{"uuid": "8f30460b-6d32-457e-9153-e19cd627d142", "historyId": "76b293fda5ee9be4309e7ffe76fe0700", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467561314, "name": "https://platform-api.dev.estrelabet.bet.br/api/profile/last-played-games GET", "stop": 1756467561314}], "attachments": [], "parameters": [], "start": 1756467561256, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 32 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, failOnStatusCode: false }\")", "stop": 1756467561314}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467561324, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756467561324}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467561324, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467561324}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467561173, "name": "GET /profile/last-played-games -> 417", "fullName": "GET /profile/last-played-games -> 417", "stop": 1756467561324}