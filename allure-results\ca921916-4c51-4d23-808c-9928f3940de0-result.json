{"uuid": "ca921916-4c51-4d23-808c-9928f3940de0", "historyId": "7386a622e43c0c5131e7e91a268e3e1e", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467557842, "name": "https://platform-api.dev.estrelabet.bet.br/api/games/get-group-games GET", "stop": 1756467557842}], "attachments": [], "parameters": [], "start": 1756467557780, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 28 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, qs: { gameCodes: '1,2,3', language: 'pt-BR' }, failOnStatusCode: false }\")", "stop": 1756467557842}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467557852, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"invalid query params\",\"code\":11}: expected 400 to equal 417", "stop": 1756467557852}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467557852, "name": "assert Esperado 417. <PERSON><PERSON>bido: 400. Body: {\"error\":\"invalid query params\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467557852}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467557707, "name": "GET /games/get-group-games -> 417", "fullName": "GET /games/get-group-games -> 417", "stop": 1756467557852}