{"uuid": "a59ca802-8dad-48f3-a182-bd23cde499c6", "historyId": "5067bc76504d46c0ecd47307d5dd49dd", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468987945, "name": "https://platform-api.dev.estrelabet.bet.br/api/games/get-lobby-group-games-paginated GET", "stop": 1756468987945}], "attachments": [], "parameters": [], "start": 1756468987717, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 44 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, qs: { language: 'pt-BR', page: 1, pageSize: 20 }, failOnStatusCode: false }\")", "stop": 1756468987945}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468987952, "name": "Assert skipped Esperado 417. Recebido: 500. Body: {\"error\":\"get lobby games group: request failed with status 500\",\"code\":10}: expected 500 to equal 417", "stop": 1756468987952}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 500}, {"name": "expected", "value": 417}], "start": 1756468987953, "name": "assert Esperado 417. Recebido: 500. Body: {\"error\":\"get lobby games group: request failed with status 500\",\"code\":10}: expected **500** to equal **417**", "stop": 1756468987953}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468987635, "name": "GET /games/get-lobby-group-games-paginated -> 417", "fullName": "GET /games/get-lobby-group-games-paginated -> 417", "stop": 1756468987953}