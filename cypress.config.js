const { defineConfig } = require("cypress")
const allureWriter = require("@shelex/cypress-allure-plugin/writer")

module.exports = defineConfig({
  //chromeWebSecurity: false,
  env: { allure: true },
  video: false,
  screenshotOnRunFailure: false,
  trashAssetsBeforeRuns: true,
  retries: 0,
  viewportWidth: 1900,
  viewportHeight: 1000,
  projectId: "26xdx8",
  e2e: {
    chromeWebSecurity: false,
    setupNodeEvents(on, config) {
      allureWriter(on, config)

      // Hook para processar resultados dos testes e adicionar dados da API
      on('after:spec', (spec, results) => {
        if (results && results.tests) {
          results.tests.forEach(test => {
            if (test.ctx && test.ctx.apiResponse) {
              // Adiciona informações da API ao contexto do teste
              const apiData = test.ctx.apiResponse;
              if (!test.context) test.context = '';

              test.context += `\n\n**API Response Details:**\n` +
                `- Status Code: ${apiData.statusCode}\n` +
                `- Method: ${apiData.method}\n` +
                `- URL: ${apiData.url}\n` +
                `- Timestamp: ${apiData.timestamp}\n` +
                `- Response: ${JSON.stringify(apiData.responseBody, null, 2)}`;
            }
          });
        }
      });

      on('before:browser:launch', (browser = {}, launchOptions) => {
        if (browser.family === 'chromium' && browser.name !== 'electron') {
          launchOptions.args.push(
            '--disable-blink-features=AutomationControlled',
            '--disable-infobars'
          );
        }
        return launchOptions;
      });

      return config
    },
    //baseUrl: '',
    specPattern: 'cypress/e2e/**/*.{js,jsx,ts,tsx}'
  },
  reporter: "mochawesome",
  reporterOptions: {
    reportDir: "cypress/results",
    overwrite: true,
    html: true,
    json: true,
    reportFilename: '[name]-report-[datetime]',
    includeScreenshots: false,
    embeddedScreenshots: false,
    inlineAssets: true

  },
});
