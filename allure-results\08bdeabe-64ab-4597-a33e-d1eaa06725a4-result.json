{"uuid": "08bdeabe-64ab-4597-a33e-d1eaa06725a4", "historyId": "338471f85e1b875b6bf24267a7ef650b", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469000346, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/active-bonus GET", "stop": 1756469000346}], "attachments": [], "parameters": [], "start": 1756468999921, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 25 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756469000346}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469000360, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756469000360}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756469000361, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756469000361}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468999864, "name": "GET /utils/active-bonus -> 417", "fullName": "GET /utils/active-bonus -> 417", "stop": 1756469000361}