{"uuid": "7d7e7abd-7b52-42e4-8700-a70b112b7495", "historyId": "5a304b6a5511efd09780839f9fc45882", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467571339, "name": "https://platform-api.dev.estrelabet.bet.br/api/management/matches POST", "stop": 1756467571339}], "attachments": [], "parameters": [], "start": 1756467571283, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 25 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756467571339}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467571344, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"Invalid matches data\",\"code\":11}: expected 400 to equal 417", "stop": 1756467571344}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467571345, "name": "assert Esperado 417. <PERSON><PERSON>bido: 400. Body: {\"error\":\"Invalid matches data\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467571345}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467571235, "name": "POST /management/matches -> 417", "fullName": "POST /management/matches -> 417", "stop": 1756467571345}