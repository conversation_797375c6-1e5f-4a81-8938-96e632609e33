{"uuid": "9cb65787-67f9-4d99-83b4-f4757aa07687", "historyId": "7e86cf88377293607b4e4302723ea000", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468983174, "name": "https://platform-api.dev.estrelabet.bet.br/api/profile/kyc-url GET", "stop": 1756468983174}], "attachments": [], "parameters": [], "start": 1756468983109, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 22 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, failOnStatusCode: false }\")", "stop": 1756468983174}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468983185, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756468983185}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468983186, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468983186}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468983050, "name": "GET /profile/kyc-url -> 417", "fullName": "GET /profile/kyc-url -> 417", "stop": 1756468983186}