{"uuid": "a091078f-fd1b-454b-a14c-3e346b12b788", "historyId": "b2fa4316f6e031ba75942e81f5dd6ab2", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467579917, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/user-policy-details POST", "stop": 1756467579917}], "attachments": [], "parameters": [], "start": 1756467579857, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 32 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756467579917}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467579924, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756467579924}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467579925, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467579925}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467579796, "name": "POST /utils/user-policy-details -> 417", "fullName": "POST /utils/user-policy-details -> 417", "stop": 1756467579925}