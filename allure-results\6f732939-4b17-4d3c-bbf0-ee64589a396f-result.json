{"uuid": "6f732939-4b17-4d3c-bbf0-ee64589a396f", "historyId": "96fdbac08f11a1e7fd83eb8a9e802033", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467575418, "name": "https://platform-api.dev.estrelabet.bet.br/api/reviews/export GET", "stop": 1756467575418}], "attachments": [], "parameters": [], "start": 1756467574382, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 21 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756467575418}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467574334, "name": "GET /reviews/export -> 417", "fullName": "GET /reviews/export -> 417", "stop": 1756467575436}