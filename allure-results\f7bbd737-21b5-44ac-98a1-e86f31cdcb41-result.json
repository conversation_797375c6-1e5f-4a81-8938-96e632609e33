{"uuid": "f7bbd737-21b5-44ac-98a1-e86f31cdcb41", "historyId": "46a79b3cfd675e3dec93860b2482793e", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467560480, "name": "https://platform-api.dev.estrelabet.bet.br/api/login/social POST", "stop": 1756467560480}], "attachments": [], "parameters": [], "start": 1756467560421, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 19 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: { provider: 'google', accessToken: 'token' }, failOnStatusCode: false }\")", "stop": 1756467560480}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467560488, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"invalid body data\",\"code\":11}: expected 400 to equal 417", "stop": 1756467560488}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467560488, "name": "assert Esperado 417. <PERSON><PERSON>bido: 400. Body: {\"error\":\"invalid body data\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467560488}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467560370, "name": "POST /login/social -> 417", "fullName": "POST /login/social -> 417", "stop": 1756467560488}