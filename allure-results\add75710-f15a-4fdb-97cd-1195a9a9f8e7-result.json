{"uuid": "add75710-f15a-4fdb-97cd-1195a9a9f8e7", "historyId": "617f8692ed1fa188415b05257ccb3ee2", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467580956, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/validateemail POST", "stop": 1756467580956}], "attachments": [], "parameters": [], "start": 1756467580725, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 26 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: { email: '<EMAIL>' }, failOnStatusCode: false }\")", "stop": 1756467580956}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467580675, "name": "POST /utils/validateemail -> 417", "fullName": "POST /utils/validateemail -> 417", "stop": 1756467580963}