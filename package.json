{"name": "EstrelaBet", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"cypress:open:qa": "cypress open --env ENV=qa", "cypress:run:qa": "npx cypress run --env ENV=qa", "cypress:run:qa:smoke": "npx cypress run --env ENV=qa,grepTags=@smoke", "cypress:open:hml": "cypress open --env ENV=hml,allure=true", "cypress:run:hml": "npx cypress run --env ENV=hml --record --key d308bb51-57bb-4121-971e-9e92d16e925c", "cypress:run:hml:smoke": "npx cypress run --env ENV=hml,grepTags=@smoke", "cypress:open:prod": "cypress open --env ENV=prod", "cypress:run:prod": "npx cypress run --env ENV=prod"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@cypress/grep": "^4.1.0", "@faker-js/faker": "^8.0.1", "@shelex/cypress-allure-plugin": "^2.41.2", "allure-commandline": "^2.34.1", "cypress": "^15.0.0"}, "dependencies": {"cypress-plugin-api": "^2.11.2", "mochawesome": "^7.1.3"}}