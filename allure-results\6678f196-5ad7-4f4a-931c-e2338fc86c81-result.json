{"uuid": "6678f196-5ad7-4f4a-931c-e2338fc86c81", "historyId": "832643ef52929d09bd7bf0dfe3b9ee23", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467574166, "name": "https://platform-api.dev.estrelabet.bet.br/api/reviews GET", "stop": 1756467574166}], "attachments": [], "parameters": [], "start": 1756467573642, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 14 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756467574166}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467573581, "name": "GET /reviews -> 417", "fullName": "GET /reviews -> 417", "stop": 1756467574190}