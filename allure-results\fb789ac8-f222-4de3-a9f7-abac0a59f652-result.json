{"uuid": "fb789ac8-f222-4de3-a9f7-abac0a59f652", "historyId": "472dc28a975f885f609141d27e8810f6", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468990374, "name": "https://platform-api.dev.estrelabet.bet.br/api/home/<USER>", "stop": 1756468990374}], "attachments": [], "parameters": [], "start": 1756468990306, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 19 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756468990374}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468990383, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"platform not recognized\",\"code\":11}: expected 400 to equal 417", "stop": 1756468990384}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468990384, "name": "assert Esperado 417. Re<PERSON>bido: 400. Body: {\"error\":\"platform not recognized\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468990384}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468990217, "name": "GET /home/<USER>", "fullName": "GET /home/<USER>", "stop": 1756468990384}