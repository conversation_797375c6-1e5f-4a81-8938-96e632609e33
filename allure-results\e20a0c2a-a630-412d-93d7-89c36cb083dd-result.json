{"uuid": "e20a0c2a-a630-412d-93d7-89c36cb083dd", "historyId": "d098378bba43067fa054beef0f69d0a9", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468998691, "name": "https://platform-api.dev.estrelabet.bet.br/api/stories GET", "stop": 1756468998691}], "attachments": [], "parameters": [], "start": 1756468998594, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 14 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, failOnStatusCode: false }\")", "stop": 1756468998691}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468998705, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"Invalid story type. Must be 'casino' or 'sportsbook'.\",\"code\":11}: expected 400 to equal 417", "stop": 1756468998705}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468998705, "name": "assert Esperado 417. <PERSON><PERSON>bido: 400. Body: {\"error\":\"Invalid story type. Must be 'casino' or 'sportsbook'.\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468998705}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468998497, "name": "GET /stories -> 417", "fullName": "GET /stories -> 417", "stop": 1756468998705}