{"uuid": "f680ecfc-550e-4bf6-9b96-cd227d923285", "historyId": "f21c58cafc94917b71975021fa7154d6", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467556189, "name": "https://platform-api.dev.estrelabet.bet.br/api/cashier/deposit/wallet-payment POST", "stop": 1756467556189}], "attachments": [], "parameters": [], "start": 1756467556128, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 37 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: { amount: 1, method: 'pix' }, failOnStatusCode: false }\")", "stop": 1756467556189}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467556198, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"invalid session\",\"code\":11}: expected 400 to equal 417", "stop": 1756467556198}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467556199, "name": "assert Esperado 417. <PERSON><PERSON>bido: 400. Body: {\"error\":\"invalid session\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467556199}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467556071, "name": "POST /cashier/deposit/wallet-payment -> 417", "fullName": "POST /cashier/deposit/wallet-payment -> 417", "stop": 1756467556199}