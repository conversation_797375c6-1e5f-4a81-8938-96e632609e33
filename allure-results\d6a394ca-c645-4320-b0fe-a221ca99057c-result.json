{"uuid": "d6a394ca-c645-4320-b0fe-a221ca99057c", "historyId": "f21c58cafc94917b71975021fa7154d6", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468977828, "name": "https://platform-api.dev.estrelabet.bet.br/api/cashier/deposit/wallet-payment POST", "stop": 1756468977828}], "attachments": [], "parameters": [], "start": 1756468977745, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 37 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: { amount: 1, method: 'pix' }, failOnStatusCode: false }\")", "stop": 1756468977828}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468977841, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"invalid session\",\"code\":11}: expected 400 to equal 417", "stop": 1756468977841}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468977841, "name": "assert Esperado 417. <PERSON><PERSON>bido: 400. Body: {\"error\":\"invalid session\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468977841}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468977682, "name": "POST /cashier/deposit/wallet-payment -> 417", "fullName": "POST /cashier/deposit/wallet-payment -> 417", "stop": 1756468977841}