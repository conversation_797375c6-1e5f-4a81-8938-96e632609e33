{"uuid": "110c4201-b93d-4ea7-bfb2-d8a0a6abbfb1", "historyId": "96bcff006be766f875265da4691fb17a", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467572261, "name": "https://platform-api.dev.estrelabet.bet.br/api/management/top-scorers POST", "stop": 1756467572261}], "attachments": [], "parameters": [], "start": 1756467572197, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 29 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756467572261}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467572267, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Re<PERSON>bido: 400. Body: {\"error\":\"Invalid top scorers data\",\"code\":11}: expected 400 to equal 417", "stop": 1756467572267}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467572268, "name": "assert Esperado 417. <PERSON><PERSON>bid<PERSON>: 400. Body: {\"error\":\"Invalid top scorers data\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467572268}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467572095, "name": "POST /management/top-scorers -> 417", "fullName": "POST /management/top-scorers -> 417", "stop": 1756467572268}