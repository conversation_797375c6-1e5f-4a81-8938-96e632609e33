{"uuid": "d24f9728-4945-4c9d-a023-62ea2dfda55d", "historyId": "5631df480280894f336a111309397f14", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468978052, "name": "https://platform-api.dev.estrelabet.bet.br/api/cashier/deposit/queue-notification POST", "stop": 1756468978052}], "attachments": [], "parameters": [], "start": 1756468977964, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 41 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: { transactionId: 'test-transaction', status: 'ok' }, failOnStatusCode: false }\")", "stop": 1756468978052}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468978060, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"invalid session\",\"code\":11}: expected 400 to equal 417", "stop": 1756468978060}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468978060, "name": "assert Esperado 417. <PERSON><PERSON>bido: 400. Body: {\"error\":\"invalid session\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468978060}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468977913, "name": "POST /cashier/deposit/queue-notification -> 417", "fullName": "POST /cashier/deposit/queue-notification -> 417", "stop": 1756468978061}