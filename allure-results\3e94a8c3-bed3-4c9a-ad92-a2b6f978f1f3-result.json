{"uuid": "3e94a8c3-bed3-4c9a-ad92-a2b6f978f1f3", "historyId": "7f9cb97afa39e6fcebf2059d8f426d79", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467560089, "name": "https://platform-api.dev.estrelabet.bet.br/api/login POST", "stop": 1756467560089}], "attachments": [], "parameters": [], "start": 1756467559868, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 12 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: { email: '<EMAIL>', password: 'Password123' }, failOnStatusCode: false }\")", "stop": 1756467560089}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467560100, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Re<PERSON>bido: 500. Body: undefined: expected 500 to equal 417", "stop": 1756467560100}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 500}, {"name": "expected", "value": 417}], "start": 1756467560101, "name": "assert Esperado 417. <PERSON><PERSON><PERSON><PERSON>: 500. Body: undefined: expected **500** to equal **417**", "stop": 1756467560101}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467559817, "name": "POST /login -> 417", "fullName": "POST /login -> 417", "stop": 1756467560101}