{"uuid": "3749f751-1daf-49dd-9839-d9de4dd32fd4", "historyId": "5067bc76504d46c0ecd47307d5dd49dd", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467565015, "name": "https://platform-api.dev.estrelabet.bet.br/api/games/get-lobby-group-games-paginated GET", "stop": 1756467565015}], "attachments": [], "parameters": [], "start": 1756467564782, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 44 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, qs: { language: 'pt-BR', page: 1, pageSize: 20 }, failOnStatusCode: false }\")", "stop": 1756467565015}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467565024, "name": "Assert skipped Esperado 417. Recebido: 500. Body: {\"error\":\"get lobby games group: request failed with status 500\",\"code\":10}: expected 500 to equal 417", "stop": 1756467565024}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 500}, {"name": "expected", "value": 417}], "start": 1756467565024, "name": "assert Esperado 417. Recebido: 500. Body: {\"error\":\"get lobby games group: request failed with status 500\",\"code\":10}: expected **500** to equal **417**", "stop": 1756467565024}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467564702, "name": "GET /games/get-lobby-group-games-paginated -> 417", "fullName": "GET /games/get-lobby-group-games-paginated -> 417", "stop": 1756467565024}