{"uuid": "3c6305fc-cf7f-4a3e-8888-38fd43a7ab55", "historyId": "615952dbed752bf77a5ca8e92e8e1947", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468983716, "name": "https://platform-api.dev.estrelabet.bet.br/api/profile/send-auth-code POST", "stop": 1756468983716}], "attachments": [], "parameters": [], "start": 1756468983644, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 29 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: { channel: 'email' }, failOnStatusCode: false }\")", "stop": 1756468983716}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468983726, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756468983726}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468983727, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468983727}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468983568, "name": "POST /profile/send-auth-code -> 417", "fullName": "POST /profile/send-auth-code -> 417", "stop": 1756468983727}