{"uuid": "7baca9a4-2489-4484-91ab-f28c3cda67f2", "historyId": "46a79b3cfd675e3dec93860b2482793e", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468982394, "name": "https://platform-api.dev.estrelabet.bet.br/api/login/social POST", "stop": 1756468982394}], "attachments": [], "parameters": [], "start": 1756468982332, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 19 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: { provider: 'google', accessToken: 'token' }, failOnStatusCode: false }\")", "stop": 1756468982394}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468982405, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"invalid body data\",\"code\":11}: expected 400 to equal 417", "stop": 1756468982405}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468982405, "name": "assert Esperado 417. <PERSON><PERSON>bido: 400. Body: {\"error\":\"invalid body data\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468982405}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468982233, "name": "POST /login/social -> 417", "fullName": "POST /login/social -> 417", "stop": 1756468982405}