{"uuid": "cb42a087-fbda-4e27-a00b-5ef9904308f4", "historyId": "a60e7aed8bc244e61222b3f70771ec84", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468986530, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/address GET", "stop": 1756468986530}], "attachments": [], "parameters": [], "start": 1756468986109, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 20 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', cep: '01001000' }, failOnStatusCode: false }\")", "stop": 1756468986530}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468986044, "name": "GET /utils/address -> 417", "fullName": "GET /utils/address -> 417", "stop": 1756468986539}