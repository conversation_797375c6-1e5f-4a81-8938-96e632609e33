{"uuid": "9d4ee588-ca85-4eed-b587-9316051d9467", "historyId": "dba2a27830cdec3ee3cc921aa44cf1f0", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467580409, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/validatecpf POST", "stop": 1756467580409}], "attachments": [], "parameters": [], "start": 1756467580307, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 24 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: { cpf: '00000000000' }, failOnStatusCode: false }\")", "stop": 1756467580409}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467580417, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Re<PERSON>bido: 500. Body: undefined: expected 500 to equal 417", "stop": 1756467580417}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 500}, {"name": "expected", "value": 417}], "start": 1756467580417, "name": "assert Esperado 417. <PERSON><PERSON><PERSON><PERSON>: 500. Body: undefined: expected **500** to equal **417**", "stop": 1756467580417}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467580206, "name": "POST /utils/validatecpf -> 417", "fullName": "POST /utils/validatecpf -> 417", "stop": 1756467580417}