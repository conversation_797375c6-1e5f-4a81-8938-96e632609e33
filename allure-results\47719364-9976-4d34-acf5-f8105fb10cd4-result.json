{"uuid": "*************-4d34-acf5-f8105fb10cd4", "historyId": "548b86897f6c0ee0c0357717411c41ed", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": *************, "name": "https://platform-api.dev.estrelabet.bet.br/api/cashier/deposit/account GET", "stop": *************}], "attachments": [], "parameters": [], "start": *************, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 30 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, qs: { deposit: '', verified: true }, failOnStatusCode: false }\")", "stop": *************}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": *************, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": *************}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": *************, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": *************}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": *************, "name": "GET /cashier/deposit/account -> 417", "fullName": "GET /cashier/deposit/account -> 417", "stop": *************}