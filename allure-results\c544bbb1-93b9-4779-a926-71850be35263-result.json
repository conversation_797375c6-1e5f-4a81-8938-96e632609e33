{"uuid": "c544bbb1-93b9-4779-a926-71850be35263", "historyId": "0325fe843240b31a1d64c867f115052f", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467571125, "name": "https://platform-api.dev.estrelabet.bet.br/api/management/bonusLed POST", "stop": 1756467571125}], "attachments": [], "parameters": [], "start": 1756467571071, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 26 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756467571125}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467571131, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 400. Body: {\"error\":\"Invalid bonus led data\",\"code\":11}: expected 400 to equal 417", "stop": 1756467571131}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756467571132, "name": "assert Esperado 417. <PERSON><PERSON><PERSON><PERSON>: 400. Body: {\"error\":\"Invalid bonus led data\",\"code\":11}: expected **400** to equal **417**", "stop": 1756467571132}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467571005, "name": "POST /management/bonusLed -> 417", "fullName": "POST /management/bonusLed -> 417", "stop": 1756467571132}