{"uuid": "76dd81a2-050e-44ca-9f99-4e5d5d8c1bbe", "historyId": "e232fe550bcebbf0e2dd23df6bdf8257", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467564543, "name": "https://platform-api.dev.estrelabet.bet.br/api/games/get-lobby-group-games GET", "stop": 1756467564543}], "attachments": [], "parameters": [], "start": 1756467563759, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 34 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, qs: { language: 'pt-BR' }, failOnStatusCode: false }\")", "stop": 1756467564543}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467564574, "name": "Assert skipped Esperado 417. Recebido: 500. Body: {\"error\":\"get lobby games group: request failed with status 500\",\"code\":10}: expected 500 to equal 417", "stop": 1756467564574}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 500}, {"name": "expected", "value": 417}], "start": 1756467564575, "name": "assert Esperado 417. Recebido: 500. Body: {\"error\":\"get lobby games group: request failed with status 500\",\"code\":10}: expected **500** to equal **417**", "stop": 1756467564575}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467563680, "name": "GET /games/get-lobby-group-games -> 417", "fullName": "GET /games/get-lobby-group-games -> 417", "stop": 1756467564575}