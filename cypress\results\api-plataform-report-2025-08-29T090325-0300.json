{"stats": {"suites": 1, "tests": 62, "passes": 62, "pending": 0, "failures": 0, "start": "2025-08-29T12:02:56.303Z", "end": "2025-08-29T12:03:25.588Z", "duration": 29285, "testsRegistered": 62, "passPercent": 100, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "317ed4e0-d2f1-483d-aacc-e293736cd2a0", "title": "", "fullFile": "cypress\\e2e\\Backend\\api-plataform.cy.js", "file": "cypress\\e2e\\Backend\\api-plataform.cy.js", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "2187fb18-4a10-4a01-9985-d8641679d89b", "title": "Platform API - smoke 417", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "GET /cashier/deposit/account -> 417", "fullTitle": "Platform API - smoke 417 GET /cashier/deposit/account -> 417", "timedOut": null, "duration": 693, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/cashier/deposit/account'),\n  headers: withSession(commonHeaders()),\n  qs: {\n    deposit: '',\n    verified: true\n  },\n  failOnStatusCode: false\n}).then(res => {\n  // Adiciona informações da API ao relatório se for sucesso\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "325d7ea2-f514-461e-80f3-768e85e24d66", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /cashier/deposit/queued-notifications -> 417", "fullTitle": "Platform API - smoke 417 GET /cashier/deposit/queued-notifications -> 417", "timedOut": null, "duration": 127, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/cashier/deposit/queued-notifications'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "ebddd058-18a1-4175-95ca-8171bde04519", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "DELETE /cashier/deposit/queued-notifications -> 417", "fullTitle": "Platform API - smoke 417 DELETE /cashier/deposit/queued-notifications -> 417", "timedOut": null, "duration": 123, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'DELETE',\n  url: api('/cashier/deposit/queued-notifications'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "08fa57d2-885e-48c9-822c-dcb6bae52dae", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /cashier/deposit/screen-info -> 417", "fullTitle": "Platform API - smoke 417 GET /cashier/deposit/screen-info -> 417", "timedOut": null, "duration": 100, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/cashier/deposit/screen-info'),\n  headers: withSession(commonHeaders()),\n  qs: {\n    platform: 'web'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "4564f93f-788d-4556-a69f-e743a4722b8d", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /cashier/deposit/wallet-payment -> 417", "fullTitle": "Platform API - smoke 417 POST /cashier/deposit/wallet-payment -> 417", "timedOut": null, "duration": 142, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/cashier/deposit/wallet-payment'),\n  headers: withSession(commonHeaders()),\n  body: {\n    amount: 1,\n    method: 'pix'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "e736a3a6-7074-471d-b0b9-fd9501bee273", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /cashier/deposit/queue-notification -> 417", "fullTitle": "Platform API - smoke 417 POST /cashier/deposit/queue-notification -> 417", "timedOut": null, "duration": 131, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/cashier/deposit/queue-notification'),\n  headers: withSession(commonHeaders()),\n  body: {\n    transactionId: 'test-transaction',\n    status: 'ok'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "8d76de3e-f458-4a77-9e40-57fdda7a74dc", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "DELETE /games/cache/all -> 417", "fullTitle": "Platform API - smoke 417 DELETE /games/cache/all -> 417", "timedOut": null, "duration": 111, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'DELETE',\n  url: api('/games/cache/all'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "2dbabb47-5a55-4051-9600-fa96dea0710f", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /games/get-free-games -> 417", "fullTitle": "Platform API - smoke 417 GET /games/get-free-games -> 417", "timedOut": null, "duration": 904, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/games/get-free-games'),\n  headers: commonHeaders(),\n  qs: {\n    gameSymbol: '',\n    language: 'pt-BR',\n    upcomingGames: false\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "18c4174d-8c45-46a1-bf9d-574afc8d8a68", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /games/get-group-games -> 417", "fullTitle": "Platform API - smoke 417 GET /games/get-group-games -> 417", "timedOut": null, "duration": 148, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/games/get-group-games'),\n  headers: commonHeaders(),\n  qs: {\n    gameCodes: '1,2,3',\n    language: 'pt-BR'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "2a7fbf67-5719-4d2b-a7cd-567f4cafd45b", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /games/get-list-group -> 417", "fullTitle": "Platform API - smoke 417 GET /games/get-list-group -> 417", "timedOut": null, "duration": 1323, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/games/get-list-group'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "22d94efe-aa63-4f38-b50e-cfe82e4e8f68", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /cashier/deposit/wallet-withdrawal -> 417", "fullTitle": "Platform API - smoke 417 POST /cashier/deposit/wallet-withdrawal -> 417", "timedOut": null, "duration": 120, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/cashier/deposit/wallet-withdrawal'),\n  headers: withSession(commonHeaders()),\n  body: {\n    amount: 1,\n    method: 'pix'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "c6ec3fde-5fa0-458b-8291-b1e44866e071", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /games/get-real-games -> 417", "fullTitle": "Platform API - smoke 417 GET /games/get-real-games -> 417", "timedOut": null, "duration": 118, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/games/get-real-games'),\n  headers: commonHeaders(),\n  qs: {\n    hasDemo: false,\n    language: 'pt-BR',\n    upcomingGames: false\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "b5ac88c6-26e5-4f45-924f-766e4e6bb745", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /login -> 417", "fullTitle": "Platform API - smoke 417 POST /login -> 417", "timedOut": null, "duration": 301, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/login'),\n  headers: commonHeaders(),\n  body: {\n    email: '<EMAIL>',\n    password: 'Password123'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "1dac12ac-f096-467e-8fad-51d42a84e182", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /login/logout -> 417", "fullTitle": "Platform API - smoke 417 POST /login/logout -> 417", "timedOut": null, "duration": 120, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/login/logout'),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "7f99a6e8-26a4-46ef-a392-bfd943aac7d6", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /login/social -> 417", "fullTitle": "Platform API - smoke 417 POST /login/social -> 417", "timedOut": null, "duration": 147, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/login/social'),\n  headers: commonHeaders(),\n  body: {\n    provider: 'google',\n    accessToken: 'token'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "e96d9071-959f-46a5-bbd2-5500dddde4df", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /profile -> 417", "fullTitle": "Platform API - smoke 417 GET /profile -> 417", "timedOut": null, "duration": 158, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/profile'),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "28d09971-2f22-4511-9319-476bbde4e1ac", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /profile/balance -> 417", "fullTitle": "Platform API - smoke 417 GET /profile/balance -> 417", "timedOut": null, "duration": 143, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/profile/balance'),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "a0d2c2c5-20b6-4024-a664-b7f0cb19bafc", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /profile/kyc-url -> 417", "fullTitle": "Platform API - smoke 417 GET /profile/kyc-url -> 417", "timedOut": null, "duration": 116, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/profile/kyc-url'),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "6b6a6425-a6a3-4901-ac1b-d26bfecb5e99", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /profile/last-played-games -> 417", "fullTitle": "Platform API - smoke 417 GET /profile/last-played-games -> 417", "timedOut": null, "duration": 109, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/profile/last-played-games'),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "989d3d68-b766-4795-93e1-3d4e4b82b31f", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /profile/send-auth-code -> 417", "fullTitle": "Platform API - smoke 417 POST /profile/send-auth-code -> 417", "timedOut": null, "duration": 136, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/profile/send-auth-code'),\n  headers: withSession(commonHeaders()),\n  body: {\n    channel: 'email'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "50a29c68-5435-490f-9ef7-5dc5c51775b0", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /profile/verify-auth-code -> 417", "fullTitle": "Platform API - smoke 417 POST /profile/verify-auth-code -> 417", "timedOut": null, "duration": 129, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/profile/verify-auth-code'),\n  headers: withSession(commonHeaders()),\n  body: {\n    code: '123456'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "0e2e0dc0-6f40-446c-ab05-2b59e82adcbc", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /promotions -> 417", "fullTitle": "Platform API - smoke 417 GET /promotions -> 417", "timedOut": null, "duration": 786, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/promotions'),\n  headers: commonHeaders(),\n  qs: {\n    languageId: 'pt',\n    hiddenPromotion: false\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "99f0a0c0-ff7e-4986-8c4a-caedc4b229e2", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /promotions/{promotionId} -> 417", "fullTitle": "Platform API - smoke 417 GET /promotions/{promotionId} -> 417", "timedOut": null, "duration": 737, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/promotions/1'),\n  headers: commonHeaders(),\n  qs: {\n    languageId: 'pt',\n    hiddenPromotion: false\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "f06f5e4e-d61d-4bc8-9695-deb9f2fa711a", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /utils/address -> 417", "fullTitle": "Platform API - smoke 417 GET /utils/address -> 417", "timedOut": null, "duration": 473, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/utils/address'),\n  headers: _objectSpread(_objectSpread({}, commonHeaders()), {}, {\n    cep: '01001000'\n  }),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "d5310a49-65f8-43c6-a33f-668fd402f799", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /games/get-lobby-group-games -> 417", "fullTitle": "Platform API - smoke 417 GET /games/get-lobby-group-games -> 417", "timedOut": null, "duration": 754, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/games/get-lobby-group-games'),\n  headers: commonHeaders(),\n  qs: {\n    language: 'pt-BR'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "8e38d24e-64e7-460c-b5ac-f3cc1cf40845", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /games/get-lobby-group-games-paginated -> 417", "fullTitle": "Platform API - smoke 417 GET /games/get-lobby-group-games-paginated -> 417", "timedOut": null, "duration": 295, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/games/get-lobby-group-games-paginated'),\n  headers: commonHeaders(),\n  qs: {\n    language: 'pt-BR',\n    page: 1,\n    pageSize: 20\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "b265d861-2747-4ad2-ab90-b70e82ca439f", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /games/search-games -> 417", "fullTitle": "Platform API - smoke 417 GET /games/search-games -> 417", "timedOut": null, "duration": 271, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/games/search-games'),\n  headers: commonHeaders(),\n  qs: {\n    language: 'pt-BR',\n    search: 'game'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "6ee18f4e-a3d1-4633-b0c5-8f9f436d156c", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /home -> 417", "fullTitle": "Platform API - smoke 417 GET /home -> 417", "timedOut": null, "duration": 302, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/home'),\n  headers: commonHeaders(),\n  qs: {\n    platform: 'web'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "5aed1c6e-**************-120aea3c28ca", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /home/<USER>", "fullTitle": "Platform API - smoke 417 GET /home/<USER>", "timedOut": null, "duration": 215, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/home/<USER>'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "965c89fc-ae22-4138-b555-694565b94bcd", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /home/<USER>", "fullTitle": "Platform API - smoke 417 GET /home/<USER>", "timedOut": null, "duration": 98, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/home/<USER>'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "736c5796-067e-4778-af7d-24d3711aa9ee", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /home/<USER>", "fullTitle": "Platform API - smoke 417 GET /home/<USER>", "timedOut": null, "duration": 136, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/home/<USER>'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "cb8a5a60-bca1-4460-8e6b-3f6576447b9f", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /home/<USER>", "fullTitle": "Platform API - smoke 417 GET /home/<USER>", "timedOut": null, "duration": 119, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/home/<USER>'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "08c9e5df-c7fc-4c43-bdd0-9ce57730476e", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /home/<USER>", "fullTitle": "Platform API - smoke 417 GET /home/<USER>", "timedOut": null, "duration": 137, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/home/<USER>'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "a30935ef-5d43-46d3-ae5c-4a0ba88d1a49", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /home/<USER>", "fullTitle": "Platform API - smoke 417 GET /home/<USER>", "timedOut": null, "duration": 240, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/home/<USER>'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "bbe4af38-6f54-488a-a4df-e44c8d9d18fd", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /home/<USER>", "fullTitle": "Platform API - smoke 417 GET /home/<USER>", "timedOut": null, "duration": 114, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/home/<USER>'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "216f4ca0-4fa3-4b6b-a168-6fee7c8ce4b0", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /home/<USER>", "fullTitle": "Platform API - smoke 417 GET /home/<USER>", "timedOut": null, "duration": 143, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/home/<USER>'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "258bc8f4-209b-4731-b335-9278936bba29", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /home/<USER>", "fullTitle": "Platform API - smoke 417 GET /home/<USER>", "timedOut": null, "duration": 905, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/home/<USER>'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "86fe2215-eab5-4c45-a84c-863825766bcd", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /management/bonusLed -> 417", "fullTitle": "Platform API - smoke 417 POST /management/bonusLed -> 417", "timedOut": null, "duration": 105, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/management/bonusLed'),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "3f5dd711-7156-4817-afe7-332d275a0d6f", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /management/matches -> 417", "fullTitle": "Platform API - smoke 417 POST /management/matches -> 417", "timedOut": null, "duration": 116, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/management/matches'),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "441f815d-32c9-44d0-955f-9933695b4c6f", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /management/sportsBetting -> 417", "fullTitle": "Platform API - smoke 417 POST /management/sportsBetting -> 417", "timedOut": null, "duration": 132, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/management/sportsBetting'),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "6ef4c772-4189-4678-a270-f4ac3f6c1de7", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /management/stories -> 417", "fullTitle": "Platform API - smoke 417 POST /management/stories -> 417", "timedOut": null, "duration": 343, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/management/stories'),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "6c818ca7-f650-4eba-bf4d-585f2d95cff1", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /management/top-scorers -> 417", "fullTitle": "Platform API - smoke 417 POST /management/top-scorers -> 417", "timedOut": null, "duration": 101, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/management/top-scorers'),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "b45719ad-ef7f-47bb-9c15-bd9a8f6d159c", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /profile/kyc-details -> 417", "fullTitle": "Platform API - smoke 417 GET /profile/kyc-details -> 417", "timedOut": null, "duration": 162, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/profile/kyc-details'),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "47a5cbad-b3b8-4ab6-98ad-5292982aeee0", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /profile/third-party-kyc -> 417", "fullTitle": "Platform API - smoke 417 POST /profile/third-party-kyc -> 417", "timedOut": null, "duration": 192, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/profile/third-party-kyc'),\n  headers: withSession(commonHeaders()),\n  body: {},\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "34c07e12-d563-44af-9913-efa29b07da48", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /profile/update-profile -> 417", "fullTitle": "Platform API - smoke 417 POST /profile/update-profile -> 417", "timedOut": null, "duration": 178, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/profile/update-profile'),\n  headers: withSession(commonHeaders()),\n  body: {},\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "bdc5ed71-f38e-4eeb-bf0e-267584a8bb22", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /reviews -> 417", "fullTitle": "Platform API - smoke 417 GET /reviews -> 417", "timedOut": null, "duration": 631, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/reviews'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "b370bdcc-d3c4-4d0c-8eb8-b61e6611a2d3", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /reviews/export -> 417", "fullTitle": "Platform API - smoke 417 GET /reviews/export -> 417", "timedOut": null, "duration": 606, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/reviews/export'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "fc11f287-266e-4971-8501-3aa39a6d7297", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /stories -> 417", "fullTitle": "Platform API - smoke 417 GET /stories -> 417", "timedOut": null, "duration": 176, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/stories'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "7f31b41b-5d71-4439-96bd-e452150567ba", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /utils -> 417", "fullTitle": "Platform API - smoke 417 POST /utils -> 417", "timedOut": null, "duration": 440, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/utils'),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "b8b136cb-1851-44d0-8330-31f4eb115404", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /utils/active-bonus -> 417", "fullTitle": "Platform API - smoke 417 GET /utils/active-bonus -> 417", "timedOut": null, "duration": 479, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/utils/active-bonus'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "f84b9c1f-ec15-4263-a12f-c2c6e4f2bd2e", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /utils/bonus -> 417", "fullTitle": "Platform API - smoke 417 GET /utils/bonus -> 417", "timedOut": null, "duration": 146, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/utils/bonus'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "1ad47893-bf06-475a-8968-09433c8b896f", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /utils/claim-bonus -> 417", "fullTitle": "Platform API - smoke 417 GET /utils/claim-bonus -> 417", "timedOut": null, "duration": 132, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/utils/claim-bonus'),\n  headers: commonHeaders(),\n  qs: {\n    accept: true\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "cf154ea2-c954-4017-9026-5db4d1f66a1d", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /utils/drop-bonus -> 417", "fullTitle": "Platform API - smoke 417 GET /utils/drop-bonus -> 417", "timedOut": null, "duration": 168, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/utils/drop-bonus'),\n  headers: commonHeaders(),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "96cdf551-9c6f-458b-bc0c-82ce3e9c8bf2", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /utils/phoneavailability -> 417", "fullTitle": "Platform API - smoke 417 POST /utils/phoneavailability -> 417", "timedOut": null, "duration": 306, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/utils/phoneavailability'),\n  headers: commonHeaders(),\n  body: {\n    phone: '+5500000000'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "d161b865-537a-4d59-a02e-ff1e4bc1d674", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /utils/referral-eligible -> 417", "fullTitle": "Platform API - smoke 417 GET /utils/referral-eligible -> 417", "timedOut": null, "duration": 163, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/utils/referral-eligible'),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "9ec28346-26d2-4097-bedc-358bd8eda8ed", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /utils/social -> 417", "fullTitle": "Platform API - smoke 417 POST /utils/social -> 417", "timedOut": null, "duration": 128, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/utils/social'),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "bebfade7-3bba-4530-abd4-894c0669b74e", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "GET /utils/user-acknowledgement -> 417", "fullTitle": "Platform API - smoke 417 GET /utils/user-acknowledgement -> 417", "timedOut": null, "duration": 136, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'GET',\n  url: api('/utils/user-acknowledgement'),\n  headers: withSession(commonHeaders()),\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "5fbc3207-0ba3-4ea9-af86-fdc1722c6a3a", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /utils/user-policy-details -> 417", "fullTitle": "Platform API - smoke 417 POST /utils/user-policy-details -> 417", "timedOut": null, "duration": 147, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/utils/user-policy-details'),\n  headers: withSession(commonHeaders()),\n  body: {},\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "5409778e-d28d-4be4-afaf-4400d132ea1e", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /utils/validatecpf -> 417", "fullTitle": "Platform API - smoke 417 POST /utils/validatecpf -> 417", "timedOut": null, "duration": 148, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/utils/validatecpf'),\n  headers: commonHeaders(),\n  body: {\n    cpf: '00000000000'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "ba15cc0a-5cab-4b2e-84af-06580e2aa8b6", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /utils/validateemail -> 417", "fullTitle": "Platform API - smoke 417 POST /utils/validateemail -> 417", "timedOut": null, "duration": 285, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/utils/validateemail'),\n  headers: commonHeaders(),\n  body: {\n    email: '<EMAIL>'\n  },\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "69e06737-9e50-48c4-bdd9-6e2077eef6cd", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /cashier/deposit/update-bank-account -> 417", "fullTitle": "Platform API - smoke 417 POST /cashier/deposit/update-bank-account -> 417", "timedOut": null, "duration": 137, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/cashier/deposit/update-bank-account'),\n  headers: withSession(commonHeaders()),\n  body: {},\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "2b6f5b52-9ff6-4136-b6c9-6337c90c938e", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}, {"title": "POST /login/social/integration -> 417", "fullTitle": "Platform API - smoke 417 POST /login/social/integration -> 417", "timedOut": null, "duration": 130, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: api('/login/social/integration'),\n  headers: commonHeaders(),\n  body: {},\n  failOnStatusCode: false\n}).then(res => {\n  addApiInfoToReport(res, this);\n  expect(res.status, `Esperado 417. Recebido: ${res.status}. Body: ${JSON.stringify(res.body)}`).to.eq(417);\n});", "err": {}, "uuid": "42ec77c5-57f4-41df-afe5-5de7dbeb6880", "parentUUID": "2187fb18-4a10-4a01-9985-d8641679d89b", "isHook": false, "skipped": false}], "suites": [], "passes": ["325d7ea2-f514-461e-80f3-768e85e24d66", "ebddd058-18a1-4175-95ca-8171bde04519", "08fa57d2-885e-48c9-822c-dcb6bae52dae", "4564f93f-788d-4556-a69f-e743a4722b8d", "e736a3a6-7074-471d-b0b9-fd9501bee273", "8d76de3e-f458-4a77-9e40-57fdda7a74dc", "2dbabb47-5a55-4051-9600-fa96dea0710f", "18c4174d-8c45-46a1-bf9d-574afc8d8a68", "2a7fbf67-5719-4d2b-a7cd-567f4cafd45b", "22d94efe-aa63-4f38-b50e-cfe82e4e8f68", "c6ec3fde-5fa0-458b-8291-b1e44866e071", "b5ac88c6-26e5-4f45-924f-766e4e6bb745", "1dac12ac-f096-467e-8fad-51d42a84e182", "7f99a6e8-26a4-46ef-a392-bfd943aac7d6", "e96d9071-959f-46a5-bbd2-5500dddde4df", "28d09971-2f22-4511-9319-476bbde4e1ac", "a0d2c2c5-20b6-4024-a664-b7f0cb19bafc", "6b6a6425-a6a3-4901-ac1b-d26bfecb5e99", "989d3d68-b766-4795-93e1-3d4e4b82b31f", "50a29c68-5435-490f-9ef7-5dc5c51775b0", "0e2e0dc0-6f40-446c-ab05-2b59e82adcbc", "99f0a0c0-ff7e-4986-8c4a-caedc4b229e2", "f06f5e4e-d61d-4bc8-9695-deb9f2fa711a", "d5310a49-65f8-43c6-a33f-668fd402f799", "8e38d24e-64e7-460c-b5ac-f3cc1cf40845", "b265d861-2747-4ad2-ab90-b70e82ca439f", "6ee18f4e-a3d1-4633-b0c5-8f9f436d156c", "5aed1c6e-**************-120aea3c28ca", "965c89fc-ae22-4138-b555-694565b94bcd", "736c5796-067e-4778-af7d-24d3711aa9ee", "cb8a5a60-bca1-4460-8e6b-3f6576447b9f", "08c9e5df-c7fc-4c43-bdd0-9ce57730476e", "a30935ef-5d43-46d3-ae5c-4a0ba88d1a49", "bbe4af38-6f54-488a-a4df-e44c8d9d18fd", "216f4ca0-4fa3-4b6b-a168-6fee7c8ce4b0", "258bc8f4-209b-4731-b335-9278936bba29", "86fe2215-eab5-4c45-a84c-863825766bcd", "3f5dd711-7156-4817-afe7-332d275a0d6f", "441f815d-32c9-44d0-955f-9933695b4c6f", "6ef4c772-4189-4678-a270-f4ac3f6c1de7", "6c818ca7-f650-4eba-bf4d-585f2d95cff1", "b45719ad-ef7f-47bb-9c15-bd9a8f6d159c", "47a5cbad-b3b8-4ab6-98ad-5292982aeee0", "34c07e12-d563-44af-9913-efa29b07da48", "bdc5ed71-f38e-4eeb-bf0e-267584a8bb22", "b370bdcc-d3c4-4d0c-8eb8-b61e6611a2d3", "fc11f287-266e-4971-8501-3aa39a6d7297", "7f31b41b-5d71-4439-96bd-e452150567ba", "b8b136cb-1851-44d0-8330-31f4eb115404", "f84b9c1f-ec15-4263-a12f-c2c6e4f2bd2e", "1ad47893-bf06-475a-8968-09433c8b896f", "cf154ea2-c954-4017-9026-5db4d1f66a1d", "96cdf551-9c6f-458b-bc0c-82ce3e9c8bf2", "d161b865-537a-4d59-a02e-ff1e4bc1d674", "9ec28346-26d2-4097-bedc-358bd8eda8ed", "bebfade7-3bba-4530-abd4-894c0669b74e", "5fbc3207-0ba3-4ea9-af86-fdc1722c6a3a", "5409778e-d28d-4be4-afaf-4400d132ea1e", "ba15cc0a-5cab-4b2e-84af-06580e2aa8b6", "69e06737-9e50-48c4-bdd9-6e2077eef6cd", "2b6f5b52-9ff6-4136-b6c9-6337c90c938e", "42ec77c5-57f4-41df-afe5-5de7dbeb6880"], "failures": [], "pending": [], "skipped": [], "duration": 16811, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "7.2.0"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "[name]-report-[datetime]", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": {"reportDir": "cypress/results", "overwrite": true, "html": true, "json": true, "reportFilename": "[name]-report-[datetime]", "includeScreenshots": false, "embeddedScreenshots": false, "inlineAssets": true}, "version": "6.2.0"}}}