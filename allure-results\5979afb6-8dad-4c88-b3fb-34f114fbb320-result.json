{"uuid": "5979afb6-8dad-4c88-b3fb-34f114fbb320", "historyId": "96bcff006be766f875265da4691fb17a", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468994407, "name": "https://platform-api.dev.estrelabet.bet.br/api/management/top-scorers POST", "stop": 1756468994407}], "attachments": [], "parameters": [], "start": 1756468994351, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 29 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756468994407}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468994411, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Re<PERSON>bido: 400. Body: {\"error\":\"Invalid top scorers data\",\"code\":11}: expected 400 to equal 417", "stop": 1756468994411}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468994411, "name": "assert Esperado 417. <PERSON><PERSON>bid<PERSON>: 400. Body: {\"error\":\"Invalid top scorers data\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468994411}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468994300, "name": "POST /management/top-scorers -> 417", "fullName": "POST /management/top-scorers -> 417", "stop": 1756468994411}