{"uuid": "c280abbc-9b99-44e7-9b38-4a976712b853", "historyId": "617f8692ed1fa188415b05257ccb3ee2", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469004653, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/validateemail POST", "stop": 1756469004653}], "attachments": [], "parameters": [], "start": 1756469004411, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 26 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: { email: '<EMAIL>' }, failOnStatusCode: false }\")", "stop": 1756469004653}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756469004363, "name": "POST /utils/validateemail -> 417", "fullName": "POST /utils/validateemail -> 417", "stop": 1756469004662}