{"uuid": "b75ec16c-7576-4dd5-aea5-b453d5d4d7ca", "historyId": "e9b7b7eece96c524ba4e4a72e78c29da", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468999629, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils POST", "stop": 1756468999629}], "attachments": [], "parameters": [], "start": 1756468999249, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 12 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756468999629}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468999641, "name": "Assert skipped Esperado 417. Recebido: 500. Body: {\"error\":\"internal server error\",\"code\":10}: expected 500 to equal 417", "stop": 1756468999641}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 500}, {"name": "expected", "value": 417}], "start": 1756468999641, "name": "assert Esperado 417. Recebido: 500. Body: {\"error\":\"internal server error\",\"code\":10}: expected **500** to equal **417**", "stop": 1756468999641}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468999179, "name": "POST /utils -> 417", "fullName": "POST /utils -> 417", "stop": 1756468999641}