{"uuid": "672d64bf-4354-404b-b5e8-593827b8dfa9", "historyId": "49548c3272252514cb24c1754fb1990d", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468995577, "name": "https://platform-api.dev.estrelabet.bet.br/api/profile/third-party-kyc POST", "stop": 1756468995577}], "attachments": [], "parameters": [], "start": 1756468995489, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 30 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: {}, failOnStatusCode: false }\")", "stop": 1756468995577}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468995589, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756468995589}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468995590, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468995590}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468995366, "name": "POST /profile/third-party-kyc -> 417", "fullName": "POST /profile/third-party-kyc -> 417", "stop": 1756468995590}