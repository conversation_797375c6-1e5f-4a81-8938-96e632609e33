{"uuid": "402043cb-ee7f-4d66-b5c9-90744414c420", "historyId": "dbf93a38d869cbc71d5af81c64c34940", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468982667, "name": "https://platform-api.dev.estrelabet.bet.br/api/profile GET", "stop": 1756468982667}], "attachments": [], "parameters": [], "start": 1756468982552, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 14 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, failOnStatusCode: false }\")", "stop": 1756468982667}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468982675, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756468982675}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468982675, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468982675}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468982497, "name": "GET /profile -> 417", "fullName": "GET /profile -> 417", "stop": 1756468982676}