{"uuid": "8da29b5e-ecf8-4c61-859f-e2c3a652e014", "historyId": "47ac45729e311372c2481b105cd85a7a", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467562931, "name": "https://platform-api.dev.estrelabet.bet.br/api/promotions/1 GET", "stop": 1756467562931}], "attachments": [], "parameters": [], "start": 1756467562234, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 19 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, qs: { languageId: 'pt', hiddenPromotion: false }, failOnStatusCode: false }\")", "stop": 1756467562931}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756467562944, "name": "<PERSON><PERSON><PERSON> skipped Esperado 417. Recebido: 424. Body: {\"error\":\"third-party failed\",\"code\":58}: expected 424 to equal 417", "stop": 1756467562944}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 424}, {"name": "expected", "value": 417}], "start": 1756467562944, "name": "assert Esperado 417. <PERSON><PERSON>bido: 424. Body: {\"error\":\"third-party failed\",\"code\":58}: expected **424** to equal **417**", "stop": 1756467562944}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756467562176, "name": "GET /promotions/{promotionId} -> 417", "fullName": "GET /promotions/{promotionId} -> 417", "stop": 1756467562944}