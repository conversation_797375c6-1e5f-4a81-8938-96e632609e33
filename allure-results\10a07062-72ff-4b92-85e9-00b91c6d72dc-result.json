{"uuid": "10a07062-72ff-4b92-85e9-00b91c6d72dc", "historyId": "e67bdb718651cb6b435baece0665213f", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468979241, "name": "https://platform-api.dev.estrelabet.bet.br/api/games/get-free-games GET", "stop": 1756468979241}], "attachments": [], "parameters": [], "start": 1756468978379, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 27 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '' }, qs: { gameSymbol: '', language: 'pt-BR', upcomingGames: false }, failOnStatusCode: false }\")", "stop": 1756468979241}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468979254, "name": "Assert skipped Esperado 417. Recebido: 500. Body: {\"error\":\"internal server error\",\"code\":10}: expected 500 to equal 417", "stop": 1756468979254}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 500}, {"name": "expected", "value": 417}], "start": 1756468979255, "name": "assert Esperado 417. Recebido: 500. Body: {\"error\":\"internal server error\",\"code\":10}: expected **500** to equal **417**", "stop": 1756468979255}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468978334, "name": "GET /games/get-free-games -> 417", "fullName": "GET /games/get-free-games -> 417", "stop": 1756468979255}