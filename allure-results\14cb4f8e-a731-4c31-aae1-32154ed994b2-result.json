{"uuid": "14cb4f8e-a731-4c31-aae1-32154ed994b2", "historyId": "81fee50842131735e0df87c469443abd", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468984096, "name": "https://platform-api.dev.estrelabet.bet.br/api/profile/verify-auth-code POST", "stop": 1756468984096}], "attachments": [], "parameters": [], "start": 1756468984030, "name": "api (\"{ method: 'POST', url: 'https://platform-api.dev.estrelabet.bet.'... 31 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, body: { code: '123456' }, failOnStatusCode: false }\")", "stop": 1756468984096}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756468984105, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756468984105}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756468984105, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756468984105}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756468983951, "name": "POST /profile/verify-auth-code -> 417", "fullName": "POST /profile/verify-auth-code -> 417", "stop": 1756468984105}