{"uuid": "3c951778-8fc0-4dbb-b80c-7f58f8f75f20", "historyId": "68954dc1c235ed279ad5a51fcd790844", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469003507, "name": "https://platform-api.dev.estrelabet.bet.br/api/utils/user-acknowledgement GET", "stop": 1756469003507}], "attachments": [], "parameters": [], "start": 1756469003413, "name": "api (\"{ method: 'GET', url: 'https://platform-api.dev.estrelabet.bet.'... 33 more characters, headers: { 'EB-Client-App': 'swagger', 'X-API-Key': '', 'X-Timestamp': '', 'X-Nonce': '', 'X-Signature': '', 'EB-PS-Session-Id': '' }, failOnStatusCode: false }\")", "stop": 1756469003507}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1756469003515, "name": "Assert skipped Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected 400 to equal 417", "stop": 1756469003515}, {"status": "failed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 417}], "start": 1756469003516, "name": "assert Esperado 417. Recebido: 400. Body: {\"error\":\"request failed as no data found on PGS\",\"code\":11}: expected **400** to equal **417**", "stop": 1756469003516}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.Backend.api-plataform.cy.js"}, {"name": "suite", "value": "Platform API - smoke 417"}], "links": [], "start": 1756469003367, "name": "GET /utils/user-acknowledgement -> 417", "fullName": "GET /utils/user-acknowledgement -> 417", "stop": 1756469003516}